package domain

import (
	common "hotel/common/protocol"

	"github.com/hashicorp/go-set/v3"
)

// BookRoom represents a room booking request with rate package and guest information
type BookRoom struct {
	RatePkgId string  `json:"ratePkgId"` // RatePkgId is the unique identifier for the rate package
	Guests    []Guest `json:"guests"`    // Guests contains the list of guests for this room
}

// BookReq represents a hotel booking request containing all necessary booking information
type BookReq struct {
	RatePkgId       string       `json:"ratePkgId" required:"true"`          // RatePkgId is obtained from HotelStaticDetail API
	Booker          Booker       `json:"booker,omitempty"`                   // Booker contains the booking contact information
	Guests          []Guest      `json:"guests" required:"true"`             // Guests contains detailed information for all guests
	Payment         Payment      `json:"payment"`                            // Payment contains payment method and details
	PlatformOrderId int64        `json:"platformOrderId" apidoc:"HotelCode"` // PlatformOrderId is the internal platform order identifier
	BookReqExtra                 // BookReqExtra contains additional booking parameters
	common.Header   `apidoc:"-"` // Header contains request metadata and authentication
}

// GetRoomCount calculates the number of unique rooms based on guest room assignments
func (b BookReq) GetRoomCount() int64 {
	s := set.New[int64](len(b.Guests))
	for _, g := range b.Guests {
		s.Insert(g.RoomIndex)
	}
	return int64(s.Size())
}

// BookReqExtra contains additional optional parameters for booking requests
type BookReqExtra struct {
	RemarkToHotel string `json:"remarkToHotel"` // RemarkToHotel contains special requests or notes for the hotel
}

// Payment represents payment information for the booking (structure to be defined)
type Payment struct{}

// BookResp represents the response from a hotel booking operation
type BookResp struct {
	SupplierOrderId string      `json:"supplierOrderId" required:"true"` // SupplierOrderId is the unique order identifier from the supplier
	OrderStatus     OrderStatus `json:"orderStatus" required:"true"`     // OrderStatus indicates the current status of the booking
}
