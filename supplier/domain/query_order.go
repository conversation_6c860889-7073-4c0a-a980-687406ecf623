package domain

import (
	"time"

	"hotel/common/i18n"
	"hotel/common/money"
	"hotel/common/types"
)

// QueryOrderReq represents a request to query a single hotel order by supplier order ID
type QueryOrderReq struct {
	SupplierOrderId string `json:"supplierOrderId"` // SupplierOrderId is the unique order identifier from the supplier
}

// QueryOrderResp represents the response containing a single hotel order
type QueryOrderResp struct {
	Order *HotelOrder `json:"order"` // Order contains the detailed hotel order information
}

// QueryOrdersReq represents a request to query multiple hotel orders
type QueryOrdersReq struct {
	Supplier         Supplier `json:"supplier"`         // Supplier specifies which supplier to query
	PlatformOrderId  int64    `json:"platformOrderId"`  // PlatformOrderId is the internal platform order identifier
	SupplierOrderIds []string `json:"supplierOrderIds"` // SupplierOrderIds contains a list of supplier order identifiers to query
}

// QueryOrdersResp represents the response containing multiple hotel orders
type QueryOrdersResp struct {
	Orders []*HotelOrder `json:"orders"` // Orders contains a list of hotel order information
}

// HotelRefundInfo contains information about refunded amounts for a hotel booking
type HotelRefundInfo struct {
	RefundedPrice money.Money `json:"refundedPrice"` // RefundedPrice is the amount that has been refunded to the customer
}

// OrderHotelInfo contains hotel information associated with an order
type OrderHotelInfo struct {
	SupplierHotelId    string `json:"supplierHotelId"` // SupplierHotelId is the hotel identifier from the supplier
	HotelStaticProfile        // HotelStaticProfile contains static hotel information
}

// OrderRoomInfo contains detailed room information for an order
type OrderRoomInfo struct {
	Room                          // Room contains basic room information
	RoomID          string        `json:"roomId"`          // RoomID is the unique room identifier
	RoomName        i18n.I18N     `json:"roomName"`        // RoomName contains the room name in multiple languages
	CheckIn         types.DateInt `json:"checkIn"`         // CheckIn is the check-in date
	CheckOut        types.DateInt `json:"checkOut"`        // CheckOut is the check-out date
	SettlementPrice money.Money   `json:"settlementPrice"` // SettlementPrice is the final settlement price for the room
	RemainQuantity  int64         `json:"remainQuantity"`  // RemainQuantity is the number of rooms remaining (not cancelled)
	RefundQuantity  int64         `json:"refundQuantity"`  // RefundQuantity is the number of rooms that have been refunded

	RoomTypeCode string `json:"roomTypeCode"` // RoomTypeCode is the supplier's room type identifier
	RatePlanID   string `json:"ratePlanId"`   // RatePlanID is the supplier's rate plan identifier
	RoomTypeName string `json:"roomTypeName"` // RoomTypeName is the name of the room type
	BedTypeID    string `json:"bedTypeId"`    // BedTypeID is the supplier's bed type identifier
}

// HotelOrder represents a complete hotel order with all associated information
type HotelOrder struct {
	Basic  *OrderBasic      `json:"basic"`  // Basic contains fundamental order information
	Hotel  *OrderHotelInfo  `json:"hotel"`  // Hotel contains hotel-specific information
	Rooms  []*OrderRoomInfo `json:"rooms"`  // Rooms contains detailed information for each booked room
	Booker *Booker          `json:"booker"` // Booker contains the person who made the booking
	Guests []*HotelGuest    `json:"guests"` // Guests contains information about all guests
}

// OrderBasic contains the fundamental information about a hotel order
type OrderBasic struct {
	Supplier           Supplier      `json:"supplier"`           // Supplier identifies which supplier processed this order
	CheckIn            types.DateInt `json:"checkIn"`            // CheckIn is the check-in date for the booking
	CheckOut           types.DateInt `json:"checkOut"`           // CheckOut is the check-out date for the booking
	PlatformOrderId    int64         `json:"platformOrderId"`    // PlatformOrderId is the internal platform order identifier
	SupplierOrderId    string        `json:"supplierOrderId"`    // SupplierOrderId is the unique order identifier from the supplier
	BookingTime        time.Time     `json:"bookingTime"`        // BookingTime is when the booking was created
	HotelConfirmNumber string        `json:"hotelConfirmNumber"` // HotelConfirmNumber is the confirmation number from the hotel
	OrderStatus        OrderStatus   `json:"orderStatus"`        // OrderStatus indicates the current status of the order
	SettlementPrice    money.Money   `json:"settlementPrice"`    // SettlementPrice is the final settlement amount
	HotelRefundInfo                  // HotelRefundInfo contains refund information if applicable
	CancelReason       string        `json:"cancelReason"` // CancelReason contains the reason if the order was cancelled
}
