package domain

import (
	"hotel/common/money"
)

// CancelReq represents a request to cancel a hotel booking
type CancelReq struct {
	SupplierOrderId string     `json:"supplierOrderId"` // SupplierOrderId is the unique order identifier from the supplier
	CancelType      CancelType `json:"cancelType"`      // CancelType specifies the type of cancellation (full, partial, etc.)
	CancelReason    string     `json:"cancelReason"`    // CancelReason contains the reason for cancellation
}

// CancelResp represents the response from a booking cancellation request
type CancelResp struct {
	ServiceFee money.Money `json:"serviceFee"` // ServiceFee is the service fee charged by supplier, not refunded
	Status     OrderStatus `json:"status"`     // Status indicates the current status of the order after cancellation
}
