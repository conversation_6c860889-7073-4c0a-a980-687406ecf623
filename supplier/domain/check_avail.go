package domain

// CheckAvailReq represents a request to check room availability for a specific rate package
type CheckAvailReq struct {
	RatePkgId     string `json:"ratePkgId"` // RatePkgId is the unique identifier for the rate package to check
	SessionOption        // SessionOption contains session-specific parameters
}

// CheckAvailStatus represents the availability status of a room rate package
type CheckAvailStatus int

const (
	CheckAvailStatusAvailable   CheckAvailStatus = 1 // CheckAvailStatusAvailable indicates the room is available for booking
	CheckAvailStatusUnavailable CheckAvailStatus = 2 // CheckAvailStatusUnavailable indicates the room is not available
)

// CheckAvailResp represents the response from a room availability check
type CheckAvailResp struct {
	Status      CheckAvailStatus `json:"status"`               // Status indicates whether the room is available or not
	RoomRatePkg *RoomRatePkg     `json:"roomRatePkg,omitzero"` // RoomRatePkg contains detailed rate package information if available
	Supplier    Supplier         `json:"supplier"`             // Supplier contains supplier information for dynamic download
}
