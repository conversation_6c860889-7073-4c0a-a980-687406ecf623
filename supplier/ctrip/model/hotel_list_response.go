package model

// HotelListResponse represents the response from a hotel list search in the Ctrip system
type HotelListResponse struct {
	BaseResp                       // BaseResp contains common response fields
	ResponseStatus *ResponseStatus `json:"responseStatus"` // ResponseStatus contains the status of the response
	HotelInfoList  []*HotelInfo    `json:"hotelInfo"`      // HotelInfoList contains detailed information for each hotel
	SearchResult   *SearchResult   `json:"searchResult"`   // SearchResult contains pagination and search metadata
	CustomInfo     *CustomInfo     `json:"customInfo"`     // CustomInfo contains additional custom information
}

// HotelInfo contains comprehensive information about a hotel
type HotelInfo struct {
	HotelBaseInfo    *HotelBaseInfo    `json:"hotelBaseInfo"`    // HotelBaseInfo contains basic hotel information
	HotelStaticInfo  *HotelStaticInfo  `json:"hotelStaticInfo"`  // HotelStaticInfo contains static hotel details
	HotelTagInfoList []*HotelTagInfo   `json:"hotelTagInfo"`     // HotelTagInfoList contains hotel tags and categories
	MinPriceRoomInfo *MinPriceRoomInfo `json:"minPriceRoomInfo"` // MinPriceRoomInfo contains minimum pricing information
	HasContractRoom  bool              `json:"hasContractRoom"`  // HasContractRoom indicates if the hotel has contracted rooms
}

// HotelBaseInfo contains basic information about a hotel
type HotelBaseInfo struct {
	HotelID      int32  `json:"hotelId"`      // HotelID is the unique identifier for the hotel
	HotelName    string `json:"hotelName"`    // HotelName is the hotel name in local language
	HotelNameEn  string `json:"hotelEnName"`  // HotelNameEn is the hotel name in English
	HotelAddress string `json:"hotelAddress"` // HotelAddress is the hotel's physical address
	HotelLogoURL string `json:"hotelLogoUrl"` // HotelLogoURL is the URL to the hotel's logo image
	HotelStar    int32  `json:"hotelStar"`    // HotelStar is the star rating of the hotel
	StarLicence  bool   `json:"starLicence"`  // StarLicence indicates if the star rating is officially licensed
}

// HotelStaticInfo contains static information about a hotel
type HotelStaticInfo struct {
	HotelFacilitiesInfo *HotelFacilitiesInfo `json:"hotelFacilitiesInfo"` // HotelFacilitiesInfo contains hotel facilities and amenities
	HotelGeoInfo        *HotelGeoInfo        `json:"hotelGeoInfo"`        // HotelGeoInfo contains geographical information
	HotelReviewInfo     *HotelReviewInfo     `json:"hotelReviewInfo"`     // HotelReviewInfo contains review scores and statistics
}

// HotelFacilitiesInfo contains information about hotel facilities and amenities
type HotelFacilitiesInfo struct {
	FacilityInfoList    []*FacilityInfo `json:"facilityInfoList"`    // FacilityInfoList contains general hotel facilities
	ChineseFriendlyList []*FacilityInfo `json:"chineseFriendlyList"` // ChineseFriendlyList contains Chinese-friendly services //apidoc:zh
}

// HotelGeoInfo contains geographical and location information for a hotel
type HotelGeoInfo struct {
	HotelMapInfoList []*HotelMapInfo `json:"hotelMapInfoList"` // HotelMapInfoList contains map coordinates and location data
	DistrictInfo     *IdNameEntity   `json:"districtInfo"`     // DistrictInfo contains district information
	CityInfo         *CityEntity     `json:"cityInfo"`         // CityInfo contains city information
	ProvinceInfo     *IdNameEntity   `json:"provinceInfo"`     // ProvinceInfo contains province information
	CountryInfo      *IdNameEntity   `json:"countryInfo"`      // CountryInfo contains country information
	ZoneInfoList     []*IdNameEntity `json:"zoneInfoList"`     // ZoneInfoList contains zone and area information
	LandMarkDistance float64         `json:"landMarkDistance"` // LandMarkDistance is the distance to nearest landmark in kilometers
}

// HotelMapInfo contains map coordinates and location data for a hotel
type HotelMapInfo struct {
	Lat     float64 `json:"lat"`     // Lat is the latitude coordinate of the hotel
	Lon     float64 `json:"lon"`     // Lon is the longitude coordinate of the hotel
	MapType string  `json:"mapType"` // MapType indicates the type of map coordinate system used
}

// CityEntity contains information about a city and its hierarchical relationships
type CityEntity struct {
	ID             int32           `json:"id"`             // ID is the unique identifier for the city
	Name           string          `json:"name"`           // Name is the name of the city
	ParentCityList []*IdNameEntity `json:"parentCityList"` // ParentCityList contains parent cities in the hierarchy
	ChildCityList  []*IdNameEntity `json:"childCityList"`  // ChildCityList contains child cities in the hierarchy
}

// HotelReviewInfo contains review scores and statistics for a hotel
type HotelReviewInfo struct {
	HotelReviewScore          float64 `json:"hotelReviewScore"`          // HotelReviewScore is the average review score for the hotel
	TotalNumberOfHotelReviews int32   `json:"totalNumberOfHotelReviews"` // TotalNumberOfHotelReviews is the total count of reviews
}

// FacilityInfo contains information about a hotel facility or amenity
type FacilityInfo struct {
	FacilityType string `json:"facilityType"` // FacilityType is the category or type of the facility
	FacilityName string `json:"facilityName"` // FacilityName is the name of the facility
}

// HotelTagInfo contains tag information for categorizing hotels
type HotelTagInfo struct {
	HotelTagList []*HotelTagInfoType `json:"hotelTagList"` // HotelTagList contains individual tags for the hotel
	HotelTagType string              `json:"hotelTagType"` // HotelTagType is the category of tags
}

// HotelTagInfoType contains detailed information about a specific hotel tag
type HotelTagInfoType struct {
	TagCode string `json:"tagCode"` // TagCode is the unique code for the tag
	Name    string `json:"name"`    // Name is the display name of the tag
	Desc    string `json:"desc"`    // Desc is the description of the tag
}

// MinPriceRoomInfo contains minimum pricing information for hotel rooms
type MinPriceRoomInfo struct {
	SaleRoomID             int64                `json:"saleRoomId"`             // SaleRoomID is the unique identifier for the sale room
	CanReserve             bool                 `json:"canReserve"`             // CanReserve indicates if the room can be reserved
	MinPriceInfo           *MinPriceInfo        `json:"minPriceInfo"`           // MinPriceInfo contains minimum pricing details
	PromotionsPriceInfo    *PromotionsPriceInfo `json:"promotionsPriceInfo"`    // PromotionsPriceInfo contains promotional pricing information
	TaxInfoList            []*TaxInfo           `json:"taxInfoList"`            // TaxInfoList contains tax information for the room
	ServiceChargeInfo      *ServiceChargeInfo   `json:"serviceChargeInfo"`      // ServiceChargeInfo contains service charge details
	MinSalePrice           float64              `json:"minSalePrice"`           // MinSalePrice is the minimum sale price excluding tax
	MinSalePriceIncludeTax float64              `json:"minSalePriceIncludeTax"` // MinSalePriceIncludeTax is the minimum sale price including tax
}

// TaxInfoEntity contains tax information for pricing calculations
type TaxInfoEntity struct {
	TaxPrice            *PriceInfo `json:"taxPrice"`            // TaxPrice contains the tax amount details
	TaxType             string     `json:"taxType"`             // TaxType indicates the type of tax applied
	IncludeInTotalPrice bool       `json:"includeInTotalPrice"` // IncludeInTotalPrice indicates if tax is included in total price
}

// MinPriceInfo contains minimum pricing information with tax details
type MinPriceInfo struct {
	AvgPriceExcludeTax *PriceInfo `json:"avgPriceExcludeTax"` // AvgPriceExcludeTax is the average price excluding tax
	AvgPriceIncludeTax *PriceInfo `json:"avgPriceIncludeTax"` // AvgPriceIncludeTax is the average price including tax
}

// PriceInfo contains detailed pricing information
type PriceInfo struct {
	OriginPriceInfo *PriceEntity `json:"originPriceInfo"` // OriginPriceInfo contains the original price details
	CustomPrice     float64      `json:"customPrice"`     // CustomPrice is the custom pricing amount
}

// PromotionsPriceInfo contains promotional pricing information
type PromotionsPriceInfo struct {
	AvgDiscountedPrice *PriceInfo `json:"avgDiscountedPrice"` // AvgDiscountedPrice is the average discounted price
}

// ServiceChargeInfo contains service charge information
type ServiceChargeInfo struct {
	ChargingStrategy  string  `json:"chargingStrategy"`  // ChargingStrategy indicates how service charges are calculated
	CustomChargePrice float64 `json:"customChargePrice"` // CustomChargePrice is the custom service charge amount
}

// SearchResult contains pagination and search metadata
type SearchResult struct {
	FirstPage  bool  `json:"firstPage"`  // FirstPage indicates if this is the first page of results
	LastPage   bool  `json:"lastPage"`   // LastPage indicates if this is the last page of results
	HotelCount int32 `json:"hotelCount"` // HotelCount is the total number of hotels found
}
