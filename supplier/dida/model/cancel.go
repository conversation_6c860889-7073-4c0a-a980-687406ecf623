package model

// CancelRequest represents a request to cancel a hotel booking in the Dida system
type CancelRequest struct {
	Header      Header `json:"Header"`      // Header contains authentication and request metadata
	BookingID   string `json:"BookingID"`   // BookingID is the unique booking identifier to cancel
	ReferenceNo string `json:"ReferenceNo"` // ReferenceNo is the booking reference number
	ConfirmID   string `json:"ConfirmID"`   // ConfirmID is the booking confirmation identifier
}

// CancelResponse represents the response from a booking cancellation request
type CancelResponse struct {
	Success *CancelSuccess `json:"Success"` // Success contains successful cancellation details
	Error   *Error         `json:"Error"`   // Error contains error information if cancellation failed
}

// CancelSuccess contains details of a successful booking cancellation
type CancelSuccess struct {
	BookingID   string `json:"BookingID"`   // BookingID is the unique identifier of the cancelled booking
	ReferenceNo string `json:"ReferenceNo"` // ReferenceNo is the reference number of the cancelled booking
	Status      string `json:"Status"`      // Status indicates the current status after cancellation
}
