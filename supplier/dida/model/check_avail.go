package model

import (
	"hotel/common/money"
	"hotel/supplier/domain"
)

// CheckAvailRequest represents a request to check room availability in the Dida system
type CheckAvailRequest struct {
	PreBook          bool              `json:"PreBook"`          // PreBook indicates whether this is a pre-booking check
	CheckInDate      string            `json:"CheckInDate"`      // CheckInDate is the check-in date in YYYY-MM-DD format
	CheckOutDate     string            `json:"CheckOutDate"`     // CheckOutDate is the check-out date in YYYY-MM-DD format
	NumOfRooms       int64             `json:"NumOfRooms"`       // NumOfRooms is the number of rooms requested
	HotelID          int64             `json:"HotelID"`          // HotelID must be uppercase to avoid IncorrectRatePlanID error //apidoc:zh
	Header           Header            `json:"Header"`           // Header contains authentication and request metadata
	OccupancyDetails []OccupancyDetail `json:"OccupancyDetails"` // OccupancyDetails contains occupancy information for each room
	Currency         string            `json:"Currency"`         // Currency is the preferred currency code for pricing
	Nationality      string            `json:"Nationality"`      // Nationality is the guest's nationality for pricing calculations
	RatePlanID       string            `json:"RatePlanID"`       // RatePlanID is the specific rate plan to check availability for
	IsNeedOnRequest  bool              `json:"IsNeedOnRequest"`  // IsNeedOnRequest indicates if on-request bookings are needed
	Metadata         string            `json:"Metadata"`         // Metadata contains additional request information
}

// OccupancyDetail contains occupancy information for a specific room
type OccupancyDetail struct {
	ChildCount      int64   `json:"ChildCount"`      // ChildCount is the number of children in the room
	AdultCount      int64   `json:"AdultCount"`      // AdultCount is the number of adults in the room
	RoomNum         int64   `json:"RoomNum"`         // RoomNum is the room number or identifier
	ChildAgeDetails []int64 `json:"ChildAgeDetails"` // ChildAgeDetails contains the ages of all children
}

// CheckAvailResponse represents the response from a room availability check
type CheckAvailResponse struct {
	Error   *Error                    `json:"Error,omitempty"` // Error contains error information if the request failed
	Success CheckAvailResponseSuccess `json:"Success"`         // Success contains the successful response data
}

// CheckAvailResponseSuccess contains the successful availability check response data
type CheckAvailResponseSuccess struct {
	PriceDetails PriceDetails `json:"PriceDetails"` // PriceDetails contains detailed pricing and availability information
}

// PriceDetails contains detailed pricing and availability information for the check
type PriceDetails struct {
	CheckOutDate string      `json:"CheckOutDate"` // CheckOutDate is the check-out date for the availability check
	CheckInDate  string      `json:"CheckInDate"`  // CheckInDate is the check-in date for the availability check
	HotelList    []HotelList `json:"HotelList"`    // HotelList contains availability details for each hotel
	ReferenceNo  string      `json:"ReferenceNo"`  // ReferenceNo is the reference number for this availability check
}

// HotelList contains availability and pricing information for a specific hotel
type HotelList struct {
	HotelID                     int64                    `json:"HotelId"`                     // HotelID is the unique identifier for the hotel
	Destination                 Destination              `json:"Destination"`                 // Destination contains location information for the hotel
	RatePlanList                []CheckAvailRatePlanList `json:"RatePlanList"`                // RatePlanList contains available rate plans for the hotel
	CancellationPolicyList      []CancellationPolicyList `json:"CancellationPolicyList"`      // CancellationPolicyList contains cancellation policies
	TotalPriceWithoutSupplement float64                  `json:"TotalPriceWithoutSupplement"` // TotalPriceWithoutSupplement is the base price without additional supplements
	TotalSupplement             float64                  `json:"TotalSupplement"`             // TotalSupplement is the total amount of additional supplements
	TotalPrice                  float64                  `json:"TotalPrice"`                  // TotalPrice is the final total price including all supplements
	HotelName                   string                   `json:"HotelName"`                   // HotelName is the name of the hotel
}

// CheckAvailRatePlanList contains detailed information about an available rate plan
type CheckAvailRatePlanList struct {
	TotalPrice             float64       `json:"TotalPrice"`             // TotalPrice is the total price for the entire stay
	RoomStatus             int64         `json:"RoomStatus"`             // RoomStatus indicates the availability status of the room
	BreakfastType          int64         `json:"BreakfastType"`          // BreakfastType indicates the type of breakfast included
	BedType                int64         `json:"BedType"`                // BedType indicates the type of bed configuration
	RoomOccupancy          RoomOccupancy `json:"RoomOccupancy"`          // RoomOccupancy contains occupancy details for the room
	PriceList              []PriceList   `json:"PriceList"`              // PriceList contains daily pricing breakdown
	IsOnRequest            bool          `json:"IsOnRequest"`            // IsOnRequest indicates if this rate requires special approval
	StandardOccupancy      int64         `json:"StandardOccupancy"`      // StandardOccupancy is the standard number of guests for this room
	PriceWithoutSupplement float64       `json:"PriceWithoutSupplement"` // PriceWithoutSupplement is the base price without supplements
	Supplement             float64       `json:"Supplement"`             // Supplement is the additional supplement amount
	InventoryCount         int64         `json:"InventoryCount"`         // InventoryCount is the number of available rooms
	MaxOccupancy           int64         `json:"MaxOccupancy"`           // MaxOccupancy is the maximum number of guests allowed
	Currency               string        `json:"Currency"`               // Currency is the currency code for the pricing
	RatePlanName           string        `json:"RatePlanName"`           // RatePlanName is the name of the rate plan
	RatePlanID             string        `json:"RatePlanID"`             // RatePlanID is the unique identifier for the rate plan
	RoomName               string        `json:"RoomName"`               // RoomName is the name of the room type
}

// ToDomain converts CheckAvailResponse to domain.RoomRatePkg
// TODO: parse price change and cancel policy change
func (r *CheckAvailResponse) ToDomain() (*domain.RoomRatePkg, error) {
	if r == nil || len(r.Success.PriceDetails.HotelList) == 0 {
		return nil, nil
	}

	// Get the first RatePlan from the first hotel's RatePlanList //apidoc:zh
	for _, hotel := range r.Success.PriceDetails.HotelList {
		if len(hotel.RatePlanList) > 0 {
			ratePlan := hotel.RatePlanList[0]
			return &domain.RoomRatePkg{
				RatePkgId: ratePlan.RatePlanID,
				SupplierRatePkgKey: domain.SupplierRatePkgKey{
					Id:   ratePlan.RatePlanID,
					Name: ratePlan.RatePlanName,
				},
				RatePlan: domain.RatePlan{
					RatePlanId: ratePlan.RatePlanID,
					Meal: domain.Meal{
						Type:        domain.MealType(ratePlan.BreakfastType),
						Description: "",
					},
					Inventory: ratePlan.InventoryCount,
					DailyRates: func() []domain.DailyRate {
						var dailyRates []domain.DailyRate
						for _, price := range ratePlan.PriceList {
							dailyRates = append(dailyRates, domain.DailyRate{
								Date:          0, // Date can be parsed from price.StayDate //apidoc:zh
								TotalRateCent: int(price.Price * 100),
							})
						}
						return dailyRates
					}(),
				},
				Rate: domain.Rate{
					FinalRate: money.Money{Amount: ratePlan.TotalPrice},
				},
				Available: ratePlan.InventoryCount > 0,
			}, nil
		}
	}

	return nil, nil
}
