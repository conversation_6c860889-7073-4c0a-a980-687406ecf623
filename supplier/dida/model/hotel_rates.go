package model

// SearchRateRequest represents a request to search for hotel rates in the Dida system
type SearchRateRequest struct {
	Header            Header            `json:"Header"`            // Header contains authentication and request metadata
	HotelIDList       []int64           `json:"HotelIDList"`       // HotelIDList contains the list of hotel IDs to search rates for
	CheckInDate       string            `json:"CheckInDate"`       // CheckInDate is the check-in date in YYYY-MM-DD format
	CheckOutDate      string            `json:"CheckOutDate"`      // CheckOutDate is the check-out date in YYYY-MM-DD format
	IsRealTime        IsRealTime        `json:"IsRealTime"`        // IsRealTime specifies whether to use real-time pricing
	RealTimeOccupancy RealTimeOccupancy `json:"RealTimeOccupancy"` // RealTimeOccupancy contains occupancy details for real-time pricing
	Currency          string            `json:"Currency"`          // Currency is the preferred currency code for pricing
	Nationality       string            `json:"Nationality"`       // Nationality is the guest's nationality for pricing calculations
}

// IsRealTime specifies real-time pricing configuration
type IsRealTime struct {
	Value     bool  `json:"Value"`     // Value indicates whether real-time pricing is enabled
	RoomCount int64 `json:"RoomCount"` // RoomCount is the number of rooms for real-time pricing
}

// RealTimeOccupancy contains occupancy details for real-time rate calculations
type RealTimeOccupancy struct {
	AdultCount      int64   `json:"AdultCount"`      // AdultCount is the number of adults per room
	ChildCount      int64   `json:"ChildCount"`      // ChildCount is the number of children per room
	ChildAgeDetails []int64 `json:"ChildAgeDetails"` // ChildAgeDetails contains the ages of all children
}

// SearchRateResponse represents the response from a hotel rate search request
type SearchRateResponse struct {
	Error   *Error                    `json:"Error,omitempty"` // Error contains error information if the request failed
	Success SearchRateResponseSuccess `json:"Success"`         // Success contains the successful response data
}

// SearchRateResponseSuccess contains the successful rate search response data
type SearchRateResponseSuccess struct {
	PriceDetails SearchRatePriceDetails `json:"PriceDetails"` // PriceDetails contains detailed pricing information
}

// SearchRatePriceDetails contains detailed pricing information for the rate search
type SearchRatePriceDetails struct {
	CheckOutDate string                       `json:"CheckOutDate"` // CheckOutDate is the check-out date for the search
	CheckInDate  string                       `json:"CheckInDate"`  // CheckInDate is the check-in date for the search
	HotelList    []SearchRatePriceDetailHotel `json:"HotelList"`    // HotelList contains pricing details for each hotel
}

// SearchRatePriceDetailHotel contains pricing details for a specific hotel
type SearchRatePriceDetailHotel struct {
	HotelID      int64                                     `json:"HotelId"`      // HotelID is the unique identifier for the hotel
	Destination  Destination                               `json:"Destination"`  // Destination contains location information for the hotel
	RatePlanList []SearchRatePriceDetailsHotelListRatePlan `json:"RatePlanList"` // RatePlanList contains available rate plans for the hotel
	HotelName    string                                    `json:"HotelName"`    // HotelName is the name of the hotel
}

// SearchRatePriceDetailsHotelListRatePlan contains detailed information about a specific rate plan
type SearchRatePriceDetailsHotelListRatePlan struct {
	TotalPrice                     float64                                            `json:"TotalPrice"`                     // TotalPrice is the total price for the entire stay
	RoomStatus                     int64                                              `json:"RoomStatus"`                     // RoomStatus indicates the availability status of the room
	BreakfastType                  int64                                              `json:"BreakfastType"`                  // BreakfastType indicates the type of breakfast included
	BedType                        int64                                              `json:"BedType"`                        // BedType indicates the type of bed configuration
	RoomOccupancy                  RoomOccupancy                                      `json:"RoomOccupancy"`                  // RoomOccupancy contains occupancy details for the room
	PriceList                      []SearchRatePriceDetailsHotelListRatePlanPriceList `json:"PriceList"`                      // PriceList contains daily pricing breakdown
	RatePlanCancellationPolicyList []RatePlanCancellationPolicyList                   `json:"RatePlanCancellationPolicyList"` // RatePlanCancellationPolicyList contains cancellation policies
	StandardOccupancy              int64                                              `json:"StandardOccupancy"`              // StandardOccupancy is the standard number of guests for this room
	InventoryCount                 int64                                              `json:"InventoryCount"`                 // InventoryCount is the number of available rooms
	MaxOccupancy                   int64                                              `json:"MaxOccupancy"`                   // MaxOccupancy is the maximum number of guests allowed
	Currency                       string                                             `json:"Currency"`                       // Currency is the currency code for the pricing
	RatePlanName                   string                                             `json:"RatePlanName"`                   // RatePlanName is the name of the rate plan
	RatePlanID                     string                                             `json:"RatePlanID"`                     // RatePlanID is the unique identifier for the rate plan
	RoomName                       string                                             `json:"RoomName"`                       // RoomName is the name of the room type
	Metadata                       string                                             `json:"Metadata"`                       // Metadata contains additional information about the rate plan
}

// SearchRatePriceDetailsHotelListRatePlanPriceList contains daily pricing information
type SearchRatePriceDetailsHotelListRatePlanPriceList struct {
	StayDate   string  `json:"StayDate"`   // StayDate is the date for this pricing entry in YYYY-MM-DD format
	Price      float64 `json:"Price"`      // Price is the room rate for this specific date
	MealAmount int64   `json:"MealAmount"` // MealAmount is the monetary value of included meals
	MealType   int64   `json:"MealType"`   // MealType indicates the type of meal plan included
}

// RatePlanCancellationPolicyList contains cancellation policy information
type RatePlanCancellationPolicyList struct {
	Amount   float64 `json:"Amount"`   // Amount is the cancellation fee for this policy period
	FromDate string  `json:"FromDate"` // FromDate is when this cancellation policy becomes effective
}
