package model

// BookingRequest represents a hotel booking request in the Dida system
type BookingRequest struct {
	Header          Header      `json:"Header"`          // Header contains authentication and request metadata
	CheckInDate     string      `json:"CheckInDate"`     // CheckInDate is the check-in date in YYYY-MM-DD format
	CheckOutDate    string      `json:"CheckOutDate"`    // CheckOutDate is the check-out date in YYYY-MM-DD format
	NumOfRooms      int64       `json:"NumOfRooms"`      // NumOfRooms is the number of rooms to book
	GuestList       []GuestList `json:"GuestList"`       // GuestList contains guest information for each room
	Contact         Contact     `json:"Contact"`         // Contact contains the booking contact information
	ClientReference string      `json:"ClientReference"` // ClientReference is the client's internal reference number
	ReferenceNo     string      `json:"ReferenceNo"`     // ReferenceNo is the booking reference number
	RatePlanID      string      `json:"RatePlanID"`      // RatePlanID is the unique identifier for the selected rate plan
}

// Name represents a person's name with first and last components
type Name struct {
	Last  string `json:"Last"`  // Last is the person's last name or surname
	First string `json:"First"` // First is the person's first name or given name
}

// GuestInfo contains detailed information about a guest
type GuestInfo struct {
	Name     Name   `json:"Name"`     // Name contains the guest's first and last name
	Age      int64  `json:"Age"`      // Age is the guest's age in years
	Passport string `json:"Passport"` // Passport is the guest's passport number
	IsAdult  bool   `json:"IsAdult"`  // IsAdult indicates whether the guest is an adult
}

// GuestList contains guest information for a specific room
type GuestList struct {
	GuestInfo []GuestInfo `json:"GuestInfo"` // GuestInfo contains detailed information for each guest in the room
	RoomNum   int64       `json:"RoomNum"`   // RoomNum is the room number or identifier
}

// Contact contains contact information for the booking
type Contact struct {
	Name    Name   `json:"Name"`    // Name contains the contact person's name
	Phone   string `json:"Phone"`   // Phone is the contact phone number
	Email   string `json:"Email"`   // Email is the contact email address
	Address string `json:"Address"` // Address is the contact's address
}

// {
//     "Success": {
//         "BookingDetails": {
//             "BookingID": "14272372993",
//             "Status": 2,
//             "CheckInDate": "2025-06-28 00:00:00",
//             "CheckOutDate": "2025-06-29 00:00:00",
//             "OrderDate": "2025-06-28 19:03:42.580",
//             "NumOfRooms": 1,
//             "TotalPrice": 133.8,
//             "GuestList": [
//                 {
//                     "GuestInfo": [
//                         {
//                             "Name": {
//                                 "First": "Test",
//                                 "Last": "User"
//                             },
//                             "Age": 0,
//                             "IsAdult": true
//                         }
//                     ],
//                     "RoomNum": 1
//                 }
//             ],
//             "Contact": {
//                 "Name": {
//                     "First": "Test",
//                     "Last": "User"
//                 },
//                 "Phone": "+0 13800138000",
//                 "Email": "<EMAIL>"
//             },
//             "CustomerRequest": "",
//             "ClientReference": "123",
//             "Hotel": {
//                 "HotelId": 1143701,
//                 "HotelName": "Shangke You (Taiyuan South Station Shanxi University)",
//                 "Destination": {
//                     "CityCode": "3592"
//                 },
//                 "RatePlanList": [
//                     {
//                         "RoomOccupancy": {
//                             "RoomNum": 1,
//                             "AdultCount": 1,
//                             "ChildCount": 0
//                         },
//                         "RoomTypeID": 10928059,
//                         "RoomName": "Superior Queen Room",
//                         "RoomName_CN": "优品大床房",
//                         "RatePlanID": "-1170148899112438984",
//                         "RatePlanName": "优品大床房",
//                         "BedType": 1,
//                         "BreakfastType": 1,
//                         "RoomStatus": 1,
//                         "Currency": "CNY",
//                         "TotalPrice": 133.8,
//                         "PriceList": [
//                             {
//                                 "Price": 133.8,
//                                 "StayDate": "2025-06-28 00:00:00",
//                                 "MealType": 1,
//                                 "MealAmount": 0
//                             }
//                         ]
//                     }
//                 ],
//                 "CancellationPolicyList": [
//                     {
//                         "FromDate": "2025-06-26T00:00:00+08:00",
//                         "Amount": 133.8
//                     }
//                 ],
//                 "Remark": "<b>Reminder</b>\r\n<ul>\r\n <li>Upon your arrival please provide valid government-issued ID to the hotel front desk to locate the accurate booking.</li>\r\n <li>Please tell front desk agent your preferred bed type if your booking comes with more than one (e.g. Double or Twin). The final arrangement is fully subject to hotel's availability.</li>\r\n <li>All special requests are not guaranteed. Please confirm your special requests with front desk upon arrival.</li>\r\n <li>Check-in time starts at 14:00:00. Check-out time ends at 12:00:00. Please check-in before the latest check-in time displayed on the booking page. If no specific check-in time is indicated, please check-in before 9 PM on the first night. If you won't be able to arrive on the first night (No Show) or plan to check-in late, please contact the hotel in advance. Otherwise, the hotel may not notify guests in any way and may cancel the entire booking without refund.</li>\r\n <li>Please be noted that some hotels charge children extra breakfast fee even when your room offers breakfast. The actual situation is subject to the hotel regulations.</li>\r\n <li>Regular tax and fees are included in this stay. Addtional charges (City tax (Europe，Malaysia), resort fees, facility fees (North America), municipal fee (Dubai), tourism Tax (Malaysia), Sales and Service Tax(SST, Malaysia), etc.) may be charged directly by the hotel; Any other fees occured in the hotel such as addtional service fees, violation fines will also be charged by the hotel directly;</li>\r\n</ul>\r\n"
//             }
//         }
//     },
//     "AuditData": {
//         "RequestClientIP": "***************",
//         "SessionID": "7b1695f8-f440-4d4d-8d9e-d419f8c3609a",
//         "RequestTime": "2025-06-28T19:03:49.818+08:00",
//         "ResponseTime": "2025-06-28T19:03:50.203+08:00",
//         "ProcessTime": 384
//     }
// }

type BookingResponse struct {
	Success *BookingSuccess `json:"Success"`
	Error   *Error          `json:"Error"`
}

type BookingSuccess struct {
	BookingDetails BookingDetail `json:"BookingDetails"`
}
type BookingDetail struct {
	BookingID    string  `json:"BookingID"`
	Status       int64   `json:"Status"`
	CheckInDate  string  `json:"CheckInDate"`
	CheckOutDate string  `json:"CheckOutDate"`
	TotalPrice   float64 `json:"TotalPrice"`
	Currency     string  `json:"Currency"`
	Hotel        Hotel   `json:"Hotel"`
}

type Hotel struct {
	HotelID                int64                `json:"HotelId"`
	HotelName              string               `json:"HotelName"`
	Destination            Destination          `json:"Destination"`
	RatePlanList           []RatePlan           `json:"RatePlanList"`
	CancellationPolicyList []CancellationPolicy `json:"CancellationPolicyList"`
	Remark                 string               `json:"Remark"`
}

type RatePlan struct {
	RoomOccupancy RoomOccupancy `json:"RoomOccupancy"`
	RoomTypeID    int64         `json:"RoomTypeID"`
	RoomName      string        `json:"RoomName"`
	RoomName_CN   string        `json:"RoomName_CN"`
	RatePlanID    string        `json:"RatePlanID"`
	RatePlanName  string        `json:"RatePlanName"`
	BedType       int64         `json:"BedType"`
	BreakfastType int64         `json:"BreakfastType"`
	RoomStatus    int64         `json:"RoomStatus"`
	Currency      string        `json:"Currency"`
	TotalPrice    float64       `json:"TotalPrice"`
	PriceList     []PriceItem   `json:"PriceList"`
}

type PriceItem struct {
	Price      float64 `json:"Price"`
	StayDate   string  `json:"StayDate"`
	MealType   int64   `json:"MealType"`
	MealAmount int64   `json:"MealAmount"`
}

type CancellationPolicy struct {
	FromDate string  `json:"FromDate"`
	Amount   float64 `json:"Amount"`
}

type Destination struct {
	CountryCode string `json:"CountryCode"`
	CityCode    string `json:"CityCode"`
}

type RoomOccupancy struct {
	AdultCount    int64 `json:"AdultCount"`
	ChildrenCount int64 `json:"ChildrenCount"`
	RoomNum       int64 `json:"RoomNum"`
}

type PriceList struct {
	Date  string  `json:"Date"`
	Price float64 `json:"Price"`
}

type CancellationPolicyList struct {
	FromDate string  `json:"FromDate"`
	ToDate   string  `json:"ToDate"`
	Amount   float64 `json:"Amount"`
}

type CludedFeeList struct {
	FeeType  string  `json:"FeeType"`
	FeeName  string  `json:"FeeName"`
	FeeValue float64 `json:"FeeValue"`
}
