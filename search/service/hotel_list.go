package service

import (
	"context"
	"sort"
	"strings"

	pkgerr "github.com/pkg/errors"
	"github.com/spf13/cast"

	"hotel/common/bizerr"
	"hotel/common/log"
	"hotel/common/types"
	"hotel/common/utils"
	contentDomain "hotel/content/domain"
	contentProto "hotel/content/protocol"
	geoProto "hotel/geography/protocol"
	"hotel/search/protocol"
	supplierDomain "hotel/supplier/domain"
)

// HotelList
// @desc: Get hotel list by criteria. Room offers could be provided if asked.
// @tags: openapi,Resource,booking.hotelbyte.com/resource
// @path: /hotelList
// @auth: required
func (s *SearchService) HotelList(ctx context.Context, req *protocol.HotelListReq) (resp *protocol.HotelListResp, err error) {
	// 基于BIOrder的简化追踪
	searchID := log.GetLogidFromContext(ctx)
	sessionID := searchID // 简化为使用searchID作为sessionID

	var queryText string
	var searchType string
	var city *string

	if req.RegionName != "" {
		queryText = req.RegionName
		searchType = "region_name"
		city = &req.RegionName
	} else if req.RegionId.Valid() {
		queryText = req.RegionId.String()
		searchType = "region_id"
	} else if len(req.HotelIds) > 0 {
		queryText = req.HotelIds.Join(",")
		searchType = "hotel_ids"
	} else {
		queryText = "nearby_search"
		searchType = "location"
	}

	// 异步追踪搜索事件，不影响主流程
	// 基于BIOrder的简化搜索统计
	go func() {
		checkInStr := req.CheckIn.Format("2006-01-02")
		checkOutStr := req.CheckOut.Format("2006-01-02")
		roomCount := int(req.RoomCount)
		guestCount := int(req.AdultCount + req.ChildrenCount)

		// 记录搜索关键指标到日志，后续可基于BIOrder扩展
		log.Infoc(context.Background(), "Search metrics: search_id=%s, session=%s, query=%s, type=%s, city=%s, check_in=%s, check_out=%s, rooms=%d, guests=%d",
			searchID, sessionID, queryText, searchType, city, checkInStr, checkOutStr, roomCount, guestCount)
	}()

	// 在响应中添加searchID用于后续追踪
	defer func() {
		if resp != nil {
			if resp.Metadata == nil {
				resp.Metadata = make(map[string]interface{})
			}
			resp.Metadata["searchId"] = searchID
			resp.Metadata["sessionId"] = sessionID

			// 更新结果数量
			if len(resp.List) > 0 {
				// 基于BIOrder的简化结果统计更新
				go func() {
					// 记录最终结果数量
					log.Infoc(context.Background(), "Search result updated: search_id=%s, final_count=%d", searchID, len(resp.List))
				}()
			}
		}
	}()

	defer func() {
		resp, err = s.hotelListSupplier(ctx, req, resp, err)
	}()
	if len(req.HotelIds) > 0 {
		resp, err := s.contentSrv.MGetHotel(ctx, &contentProto.MGetHotelReq{HotelIds: req.HotelIds})
		if err != nil {
			return nil, err
		}

		// 如果需要房型信息，通过缓存服务丰富价格信息
		enrichedHotels := resp.Hotels
		if req.RequireRooms && s.priceCacheSrv != nil {
			// 转换为SupplierHotel格式进行价格丰富
			supplierHotels := s.convertToSupplierHotels(resp.Hotels)
			enrichedSupplierHotels := s.priceCacheSrv.EnrichHotelsWithPrices(ctx, req, supplierHotels)
			enrichedHotels = s.convertFromSupplierHotels(enrichedSupplierHotels)
		}

		return &protocol.HotelListResp{
			List: enrichedHotels,
		}, nil
	}
	if req.RegionId == 0 && req.RegionName != "" {
		log.Infoc(ctx, "HotelList fuzzySearch %s", req.RegionName)
		resp, err := s.geoSrv.FuzzySearch(ctx, &geoProto.FuzzySearchReq{Keyword: req.RegionName})
		if err != nil {
			return nil, err
		}
		if len(resp.Candidates) == 0 {
			return nil, bizerr.NotFoundErr.WithMessagef("not found by %s", req.RegionName)
		}
		selectedRegion := resp.Candidates[0].Region
		log.Infoc(ctx, "fuzzySearch got first selectedRegion %#v", selectedRegion)
		req.RegionId = selectedRegion.ID
	}

	if req.RegionId.Valid() {
		log.Infoc(ctx, "goto HotelIdList %v", req.RegionId)
		resp, err := s.contentSrv.ListHotelByRegionIds(ctx, &contentProto.ListHotelByRegionIdsReq{
			Operator:          req.Operator,
			RegionIds:         types.IDs{req.RegionId},
			PageReq:           req.PageReq,
			InternalSuppliers: req.InternalSuppliers,
		})
		if err != nil {
			return nil, pkgerr.Wrap(err, "hotelIdList")
		}
		log.Infoc(ctx, "got HotelIdList resp: count(%d) ids(%v) body(%s)", len(resp.Hotels), resp.Hotels.IDs(), utils.ToJSON(resp))
		// Apply enhanced filtering and sorting
		filteredHotels := s.applyHotelFilters(ctx, resp.Hotels, req)
		sortedHotels := s.applySorting(ctx, filteredHotels, req)

		return &protocol.HotelListResp{
			List:     sortedHotels,
			PageResp: resp.PageResp,
		}, nil
	}
	if req.Distance != nil {
		masterHotels, err := s.contentSrv.FindNearbyHotel(ctx, &req.Distance.Nearby)
		if err != nil {
			return nil, pkgerr.Wrap(err, "failed to find nearby")
		}
		return &protocol.HotelListResp{
			List: masterHotels,
		}, nil
	}

	if len(req.InternalSuppliers) > 0 {
		resp, err := s.contentSrv.ListHotelByRegionIds(ctx, &contentProto.ListHotelByRegionIdsReq{
			Operator:          req.Operator,
			PageReq:           req.PageReq,
			InternalSuppliers: req.InternalSuppliers,
		})
		if err != nil {
			return nil, pkgerr.Wrap(err, "hotelIdList")
		}
		log.Infoc(ctx, "got HotelIdList resp: count(%d) ids(%v) body(%s)", len(resp.Hotels), resp.Hotels.IDs(), utils.ToJSON(resp))
		// 如果需要房型信息，通过缓存服务丰富价格信息
		enrichedHotels := resp.Hotels
		if req.RequireRooms && s.priceCacheSrv != nil {
			supplierHotels := s.convertToSupplierHotels(resp.Hotels)
			enrichedSupplierHotels := s.priceCacheSrv.EnrichHotelsWithPrices(ctx, req, supplierHotels)
			enrichedHotels = s.convertFromSupplierHotels(enrichedSupplierHotels)
		}

		return &protocol.HotelListResp{
			List:     enrichedHotels,
			PageResp: resp.PageResp,
		}, nil
	}

	return &protocol.HotelListResp{}, nil
}

// convertToSupplierHotels 转换contentDomain.Hotel为SupplierHotel格式
func (s *SearchService) convertToSupplierHotels(hotels contentDomain.HotelList) []supplierDomain.SupplierHotel {
	result := make([]supplierDomain.SupplierHotel, 0, len(hotels))

	for _, hotel := range hotels {
		// 从酒店的供应商映射中获取供应商信息
		// 这里需要根据实际的数据结构调整
		supplierHotel := supplierDomain.SupplierHotel{
			Name:            hotel.Name,
			SupplierHotelId: cast.ToString(hotel.ID),           // 简化处理，实际需要获取供应商酒店ID
			Supplier:        supplierDomain.Supplier_Simulator, // 默认值，实际需要从映射关系获取
			HotelStaticProfile: supplierDomain.HotelStaticProfile{
				Name: hotel.Name,
				Star: hotel.Star,
			},
			HotelDynamicProfile: supplierDomain.HotelDynamicProfile{
				IsAvailable: true,
			},
		}
		result = append(result, supplierHotel)
	}

	return result
}

// convertFromSupplierHotels 转换SupplierHotel为contentDomain.Hotel格式
func (s *SearchService) convertFromSupplierHotels(supplierHotels []supplierDomain.SupplierHotel) contentDomain.HotelList {
	result := make(contentDomain.HotelList, 0, len(supplierHotels))

	for _, supplierHotel := range supplierHotels {
		// 这里需要根据实际需求转换回contentDomain.Hotel格式
		// 主要是将价格信息填充到Hotel结构中
		hotel := &contentDomain.Hotel{
			HotelStaticProfile:  supplierHotel.HotelStaticProfile,
			HotelDynamicProfile: supplierHotel.HotelDynamicProfile,
			// 其他字段根据需要填充
		}

		// 如果有价格信息，可以添加到Hotel的扩展字段中
		// 或者根据前端需求调整数据结构

		result = append(result, hotel)
	}

	return result
}

// applyHotelFilters applies additional filters to hotel list
func (s *SearchService) applyHotelFilters(ctx context.Context, hotels contentDomain.HotelList, req *protocol.HotelListReq) contentDomain.HotelList {
	if len(hotels) == 0 {
		return hotels
	}

	filtered := make(contentDomain.HotelList, 0, len(hotels))

	for _, hotel := range hotels {
		// Hotel name filter
		if req.HotelName != "" {
			if !s.matchesHotelName(hotel, req.HotelName) {
				continue
			}
		}

		// Rating filter
		if req.Rating != nil {
			if hotel.Rating < float64(*req.Rating) {
				continue
			}
		}

		// Price filters
		if req.MinPrice != nil && hotel.MinPrice.Amount > 0 {
			if hotel.MinPrice.Amount < *req.MinPrice {
				continue
			}
		}

		if req.MaxPrice != nil && hotel.MinPrice.Amount > 0 {
			if hotel.MinPrice.Amount > *req.MaxPrice {
				continue
			}
		}

		filtered = append(filtered, hotel)
	}

	return filtered
}

// matchesHotelName checks if hotel name matches the search term
func (s *SearchService) matchesHotelName(hotel *contentDomain.Hotel, searchTerm string) bool {
	if hotel.Name.En != "" && containsIgnoreCase(hotel.Name.En, searchTerm) {
		return true
	}
	if hotel.Name.Zh != "" && containsIgnoreCase(hotel.Name.Zh, searchTerm) {
		return true
	}
	// Check other language variations as needed
	return false
}

// containsIgnoreCase checks if s contains substr case-insensitively
func containsIgnoreCase(s, substr string) bool {
	return strings.Contains(strings.ToLower(s), strings.ToLower(substr))
}

// applySorting applies sorting to the hotel list
func (s *SearchService) applySorting(ctx context.Context, hotels contentDomain.HotelList, req *protocol.HotelListReq) contentDomain.HotelList {
	if len(hotels) == 0 || req.SortBy == "" {
		return hotels
	}

	// Create a copy to avoid modifying the original slice
	sorted := make(contentDomain.HotelList, len(hotels))
	copy(sorted, hotels)

	switch req.SortBy {
	case "price-asc":
		sort.Sort(sorted) // Uses the Less method from HotelList which sorts by MinPrice
	case "price-desc":
		sort.Sort(sort.Reverse(sorted))
	case "rating-desc":
		sort.Slice(sorted, func(i, j int) bool {
			return sorted[i].Rating > sorted[j].Rating
		})
	case "distance-asc":
		// Distance sorting would require location context
		// For now, keep original order
	default:
		// Keep original order for unknown sort types
	}

	return sorted
}
