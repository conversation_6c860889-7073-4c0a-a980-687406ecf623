package service

import (
	"context"
	rmDomain "hotel/mapping/supplier/olivier/domain"
	supplierDomain "hotel/supplier/domain"
	"strings"

	"github.com/spf13/cast"
)

// integrateRoomMapping constructs room mapping requests and applies results
func (s *SearchService) integrateRoomMapping(ctx context.Context, iso2 string, rooms []supplierDomain.Room) []supplierDomain.Room {
	if len(rooms) == 0 {
		return rooms
	}

	// Phase 1: Try Olivier room matching service
	if s.olivierClient != nil {
		roomsWithOlivierMapping := s.applyOlivierMapping(ctx, iso2, rooms)
		rooms = roomsWithOlivierMapping
	}

	// Phase 2: Enhance with GIATA room mapping (if available)
	if s.giataClient != nil {
		roomsWithGiataMapping := s.applyGiataMapping(ctx, iso2, rooms)
		rooms = roomsWithGiataMapping
	}

	return rooms
}

// applyOlivierMapping applies Olivier room matching service
func (s *SearchService) applyOlivierMapping(ctx context.Context, iso2 string, rooms []supplierDomain.Room) []supplierDomain.Room {
	var rmReq rmDomain.RoomMatchingReq
	rmReq.Iso2 = iso2
	for _, r := range rooms {
		for _, pkg := range r.Rates {
			rmReq.List = append(rmReq.List, rmDomain.RoomInfo{
				HotelID:   r.SupplierHotelId,
				Supplier:  getSupplierNameFromRatePkgId(pkg.RatePkgId), // Extract supplier from encoded RatePkgId
				RoomCode1: r.RoomTypeID,
				RoomCode2: pkg.RatePkgId,
				Name:      r.RoomName.En,
			})
		}
	}
	resp, err := s.olivierClient.RoomMatching(ctx, &rmReq)
	if err != nil || resp == nil {
		return rooms // ignore mapping failure, log in production
	}
	// 构造索引: hotelID+roomCode -> RoomKey
	mappingIndex := make(map[string]rmDomain.RoomKey)
	for _, match := range resp.Matching {
		for _, sup := range match.Suppliers {
			key := sup.HotelID + "|" + sup.RoomKey.RoomCode // use old roomCode as key
			mappingIndex[key] = sup.RoomKey
		}
	}
	// 应用映射
	for i := range rooms {
		key := rooms[i].SupplierHotelId + "|" + rooms[i].RoomTypeID
		if rk, ok := mappingIndex[key]; ok {
			if rk.RoomID != "" {
				rooms[i].RoomTypeID = rk.RoomID
				if rooms[i].MappingProcess == nil {
					rooms[i].MappingProcess = &supplierDomain.RoomMappingProcess{}
				}
				rooms[i].MappingProcess.MappedRoomID = rk.RoomID
				rooms[i].MappingProcess.OlivierMapped = true // Track Olivier mapping
			}
			if rooms[i].MappingProcess == nil {
				rooms[i].MappingProcess = &supplierDomain.RoomMappingProcess{}
			}
			rooms[i].MappingProcess.Confidence = rk.Confidence
		}
	}
	return rooms
}

// applyGiataMapping applies GIATA room mapping for additional standardization
func (s *SearchService) applyGiataMapping(ctx context.Context, iso2 string, rooms []supplierDomain.Room) []supplierDomain.Room {
	// Build GIATA mapping request
	var rmReq rmDomain.RoomMatchingReq
	rmReq.Iso2 = iso2
	for _, r := range rooms {
		for _, pkg := range r.Rates {
			rmReq.List = append(rmReq.List, rmDomain.RoomInfo{
				HotelID:   r.SupplierHotelId,
				Supplier:  getSupplierNameFromRatePkgId(pkg.RatePkgId),
				RoomCode1: r.RoomTypeID,
				RoomCode2: pkg.RatePkgId,
				Name:      r.RoomName.En,
			})
		}
	}

	resp, err := s.giataClient.RoomMatching(ctx, &rmReq)
	if err != nil || resp == nil {
		return rooms // ignore GIATA mapping failure
	}

	// Apply GIATA mapping enhancements
	mappingIndex := make(map[string]rmDomain.RoomKey)
	for _, match := range resp.Matching {
		for _, sup := range match.Suppliers {
			key := sup.HotelID + "|" + sup.RoomKey.RoomCode
			mappingIndex[key] = sup.RoomKey
		}
	}

	for i := range rooms {
		key := rooms[i].SupplierHotelId + "|" + rooms[i].RoomTypeID
		if rk, ok := mappingIndex[key]; ok {
			if rooms[i].MappingProcess == nil {
				rooms[i].MappingProcess = &supplierDomain.RoomMappingProcess{}
			}
			// Enhance with GIATA standardization
			rooms[i].MappingProcess.GiataMapped = true
			rooms[i].MappingProcess.GiataConfidence = rk.Confidence
			// If GIATA provides better room ID, use it
			if rk.RoomID != "" && rk.Confidence > rooms[i].MappingProcess.Confidence {
				rooms[i].MappingProcess.MappedRoomID = rk.RoomID
				rooms[i].MappingProcess.Confidence = rk.Confidence
				rooms[i].RoomTypeID = rk.RoomID
			}
		}
	}

	return rooms
}

// getSupplierNameFromRatePkgId extracts supplier name from encoded RatePkgId
// RatePkgId format: "supplierID:originalRatePkgId"
func getSupplierNameFromRatePkgId(encodedRatePkgId string) string {
	parts := strings.Split(encodedRatePkgId, ":")
	if len(parts) != 2 {
		return "unknown"
	}
	supplierID := cast.ToInt64(parts[0])
	supplier := supplierDomain.Supplier(supplierID)
	return supplier.String()
}
