<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>供应商多选测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
</head>
<body>
    <div id="app">
        <div style="padding: 20px; max-width: 800px; margin: 0 auto;">
            <h2>酒店供应商多选筛选测试</h2>
            
            <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 8px; font-weight: bold;">供应商选择:</label>
                <el-select
                    v-model="selectedSuppliers"
                    placeholder="选择供应商"
                    multiple
                    clearable
                    collapse-tags
                    collapse-tags-tooltip
                    style="width: 100%"
                    @change="handleSupplierChange"
                >
                    <el-option
                        v-for="supplier in suppliers"
                        :key="supplier.id"
                        :label="supplier.name"
                        :value="supplier.id"
                        :disabled="!supplier.isActive"
                    >
                        <span>{{ supplier.name }}</span>
                        <span v-if="!supplier.isActive" style="color: #999; font-size: 12px; margin-left: 8px">
                            (不可用)
                        </span>
                    </el-option>
                </el-select>
            </div>

            <div style="margin-bottom: 20px;">
                <el-checkbox
                    v-model="onlyAvailableSuppliers"
                    :disabled="selectedSuppliers.length > 0"
                    @change="handleAvailableSuppliersChange"
                >
                    仅可用供应商
                </el-checkbox>
            </div>

            <div style="margin-bottom: 20px;">
                <el-button type="primary" @click="handleSearch">搜索酒店</el-button>
            </div>

            <div style="background: #f5f5f5; padding: 15px; border-radius: 4px;">
                <h3>当前选择状态:</h3>
                <p><strong>选中的供应商:</strong> {{ selectedSuppliers.length > 0 ? selectedSuppliers.join(', ') : '无' }}</p>
                <p><strong>仅可用供应商:</strong> {{ onlyAvailableSuppliers ? '是' : '否' }}</p>
                <p><strong>搜索参数:</strong></p>
                <pre>{{ JSON.stringify(searchParams, null, 2) }}</pre>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, reactive, computed } = Vue;
        const { ElSelect, ElOption, ElCheckbox, ElButton, ElMessage } = ElementPlus;

        createApp({
            components: {
                ElSelect,
                ElOption,
                ElCheckbox,
                ElButton
            },
            setup() {
                // 模拟供应商数据
                const suppliers = ref([
                    { id: 1, name: 'Booking.com', isActive: true },
                    { id: 2, name: 'Expedia', isActive: true },
                    { id: 3, name: 'Agoda', isActive: false },
                    { id: 4, name: 'Hotels.com', isActive: true },
                    { id: 5, name: 'TBO', isActive: true },
                    { id: 6, name: 'Netstorming', isActive: false }
                ]);

                const selectedSuppliers = ref([]);
                const onlyAvailableSuppliers = ref(false);

                const searchParams = computed(() => {
                    const params = {};
                    
                    if (selectedSuppliers.value.length > 0) {
                        params.internalSuppliers = selectedSuppliers.value;
                    } else if (onlyAvailableSuppliers.value) {
                        const activeSuppliers = suppliers.value.filter(s => s.isActive).map(s => s.id);
                        params.internalSuppliers = activeSuppliers;
                    }
                    
                    return params;
                });

                const handleSupplierChange = (supplierIds) => {
                    console.log('Suppliers changed to:', supplierIds);
                    // 当选择了特定供应商时，禁用"仅可用供应商"选项
                    if (supplierIds && supplierIds.length > 0) {
                        onlyAvailableSuppliers.value = false;
                    }
                };

                const handleAvailableSuppliersChange = (value) => {
                    console.log('Available suppliers only changed to:', value);
                    // 当启用"仅可用供应商"时，清空特定供应商选择
                    if (value) {
                        selectedSuppliers.value = [];
                    }
                };

                const handleSearch = () => {
                    console.log('Search with params:', searchParams.value);
                    ElMessage.success(`搜索参数: ${JSON.stringify(searchParams.value)}`);
                };

                return {
                    suppliers,
                    selectedSuppliers,
                    onlyAvailableSuppliers,
                    searchParams,
                    handleSupplierChange,
                    handleAvailableSuppliersChange,
                    handleSearch
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
