# Hotel-BE 项目 TODO 任务清单

## 高优先级任务 (P0)

### API 模块
- **文件**: `api/service/view.go:51`
- **任务**: 添加日志记录
- **描述**: 在 starlingSrv 加载失败时添加详细的日志记录，便于问题排查
- **状态**: 待实现

### 构建工具
- **文件**: `build/api/docgen.go:120`
- **任务**: 实现 upload 函数
- **描述**: 实现文档上传功能，支持将本地文档上传到远程服务器
- **状态**: 待实现

### CQRS 模块
- **文件**: `common/cqrs/cqrs.go:82,125`
- **任务**: 实现 NSQ 适配器
- **描述**: 实现 NSQ Producer 和 Consumer 适配器，支持 NSQ 消息队列
- **状态**: 待实现

### 搜索模块
- **文件**: `search/service/hotel_price_cache.go:246,261,282`
- **任务**: 实现价格获取逻辑
- **描述**: 
  - 实现通过 HotelList 获取价格的逻辑
  - 实现批量 HotelRates 查询逻辑
  - 实现限流的单个 HotelRates 查询
- **状态**: 待实现

### 交易模块
- **文件**: `trade/service/rebooking_api.go:56`
- **任务**: 实现重预订统计功能
- **描述**: 查询数据库，统计重预订的各种指标（成功率、节省金额等）
- **状态**: 待实现

- **文件**: `trade/service/rebooking_service.go:194`
- **任务**: 实现配置存储和应用逻辑
- **描述**: 实现重预订配置的存储和应用，包括启用/禁用特定供应商、时间窗口设置等
- **状态**: 待实现

- **文件**: `trade/service/cancel_audit.go:160`
- **任务**: 完善审计日志系统
- **描述**: 在实际项目中实现：
  1. 写入专门的审计日志表
  2. 发送到审计日志系统（如ELK）
  3. 发送到监控系统（如Prometheus）
  4. 发送到告警系统
- **状态**: 待实现

- **文件**: `trade/service/order_scanner.go:40`
- **任务**: 重构以适配新的 CQRS 接口
- **描述**: 重构订单扫描器，使其适配新的 CQRS 消息发布接口
- **状态**: 待实现

- **文件**: `trade/service/book_consumer.go:136`
- **任务**: 从 checkAvailResp 获取实际供应商ID
- **描述**: 修改硬编码的供应商ID，从 checkAvailResp 中获取实际的供应商ID
- **状态**: 待实现

## 中优先级任务 (P1)

### 用户模块
- **文件**: `user/service/invite.go:107`
- **任务**: 实现重新发送邀请功能
- **描述**: 通过邀请的审计日志找到前一条邀请，续期，然后重新发送邀请邮件
- **状态**: 待实现

- **文件**: `user/service/invite.go:241,244`
- **任务**: 精细的域名管理
- **描述**: 实现精细的域名管理功能，支持不同用户类型的域名配置
- **状态**: 待实现

### 供应商模块
- **文件**: `mapping/supplier/giata/service.go:81`
- **任务**: GIATA酒店ID列表获取逻辑
- **描述**: 根据实际API实现GIATA酒店ID列表获取逻辑
- **状态**: 待实现

- **文件**: `supplier/ctrip/book.go:13,32`
- **任务**: 实现请求构建
- **描述**: 
  - 实现 CheckAvail 的请求构建
  - 实现 Cancel 的请求构建
- **状态**: 待实现

- **文件**: `supplier/netstorming/serviceutil.go:28`
- **任务**: 儿童年龄对齐
- **描述**: 实现儿童年龄对齐逻辑，确保不同供应商的儿童年龄标准一致
- **状态**: 待实现

- **文件**: `supplier/dida/model/check_avail.go:80`
- **任务**: 解析价格变化和取消政策变化
- **描述**: 实现价格变化和取消政策变化的解析逻辑
- **状态**: 待实现

- **文件**: `supplier/dida/service.go:78`
- **任务**: 国家代码搜酒店ID列表
- **描述**: 实现通过国家代码搜索酒店ID列表的功能
- **状态**: 待实现

- **文件**: `supplier/factory.go:49`
- **任务**: 自动化发现注册
- **描述**: 实现自动化发现注册功能，只能依赖数据库
- **状态**: 待实现

### 搜索模块
- **文件**: `search/service/hotel_rates.go:244,259`
- **任务**: 动态修改房间能力和房间映射集成
- **描述**: 
  - 实现动态修改 rooms 的能力
  - 集成 room-mapping 功能
- **状态**: 待实现

### 内容模块
- **文件**: `content/mysql/hotel_dao.go:281`
- **任务**: 统一搜索币种转换
- **描述**: 将价格转换为统一的搜索币种
- **状态**: 待实现

- **文件**: `content/service/database.go:115`
- **任务**: 优化酒店选择逻辑
- **描述**: 改进酒店选择逻辑，避免只取第一个酒店
- **状态**: 待实现

### 交易模块
- **文件**: `trade/service/book.go:97`
- **任务**: 数据库保存重试机制
- **描述**: 实现数据库保存的重试机制
- **状态**: 待实现

- **文件**: `trade/service/cancel_consumer.go:173`
- **任务**: 计算实际处理时间
- **描述**: 实现实际处理时间的计算，而不是使用硬编码值
- **状态**: 待实现

## 低优先级任务 (P2)

### 用户模块
- **文件**: `user/domain/user.go:20,65,248,403`
- **任务**: 用户角色和权限优化
- **描述**: 
  - 改进角色管理机制
  - 优化当前确认逻辑，支持扩展
  - 优化 scope 结构，支持 path 单分支结构
  - 评估名称是否合适
- **状态**: 待优化

- **文件**: `user/domain/entity.go:54,60`
- **任务**: 数组越界处理和缓存优化
- **描述**: 
  - 处理数组越界问题
  - 为 GetScope 方法添加缓存
- **状态**: 待优化

- **文件**: `user/mysql/entity_dao.go:78`
- **任务**: 添加缓存
- **描述**: 为实体DAO添加缓存机制
- **状态**: 待优化

- **文件**: `user/mysql/supplier_credential_dao.go:18`
- **任务**: 添加缓存
- **描述**: 为供应商凭证DAO添加缓存机制
- **状态**: 待优化

- **文件**: `user/protocol/invite.go:36,39,46`
- **任务**: 登录态和角色优化
- **描述**: 
  - 优化登录态固定字段
  - 完善下面的逻辑
  - 直接使用 role，userRole 应该用在别处
- **状态**: 待优化

- **文件**: `user/protocol/user.go:12`
- **任务**: 默认值处理
- **描述**: 优化默认值处理逻辑
- **状态**: 待优化

- **文件**: `user/service/user.go:60`
- **任务**: 检查请求实体ID
- **描述**: 实现 req.EntityIDs 的检查逻辑
- **状态**: 待优化

### 地理模块
- **文件**: `geography/domain/ctrip_hotel_city.go:250,251`
- **任务**: 完善国家代码信息
- **描述**: 完善 CountryCode 和 CountrySubdivisionCode 字段
- **状态**: 待完善

- **文件**: `geography/service/init.go:41`
- **任务**: 初始化优化
- **描述**: 优化地理模块的初始化逻辑
- **状态**: 待优化

### BI 模块
- **文件**: `bi/domain/ctx.go:48`
- **任务**: 工具方法补充
- **描述**: 补充 utils 方法，用于方便地往 context 里插入 hbLog 的字段
- **状态**: 待补充

### 构建工具
- **文件**: `build/api/asthelper/error_code.go:19,36`
- **任务**: 错误码优化
- **描述**: 
  - 处理一个 httpcode 可能有多个的情况
  - 考虑对外对内错误码分开，根据 code 大小判断
- **状态**: 待优化

- **文件**: `build/api/asthelper/parser.go:110`
- **任务**: 自定义扩展
- **描述**: 实现自定义扩展功能
- **状态**: 待实现

- **文件**: `build/api/asthelper/response_builder.go:25`
- **任务**: 响应构建优化
- **描述**: 优化响应构建逻辑
- **状态**: 待优化

- **文件**: `build/api/asthelper/comment.go:198`
- **任务**: 插件自定义
- **描述**: 实现插件自定义功能
- **状态**: 待实现

- **文件**: `build/api/asthelper/type_parser.go:98`
- **任务**: 泛型参数支持
- **描述**: 扩展支持多个泛型参数，目前仅支持单个
- **状态**: 待扩展

- **文件**: `build/api/docgen.go:341`
- **任务**: 文档生成优化
- **描述**: 优化文档生成逻辑
- **状态**: 待优化

### 通用模块
- **文件**: `common/pdf/pdf.go:80`
- **任务**: 中文字体支持
- **描述**: 添加中文字体支持
- **状态**: 待实现

### 前端模块
- **文件**: `admin-fe/src/views/system/logs/components/LogDetailDialog.vue:305`
- **任务**: 实现单个日志导出
- **描述**: 实现单个日志的导出功能
- **状态**: 待实现

- **文件**: `admin-fe/src/views/booking/hotel-search/index.vue:585`
- **任务**: 实现画廊视图
- **描述**: 实现酒店搜索结果的画廊视图展示
- **状态**: 待实现

## 测试相关任务

### 供应商测试
- **文件**: `supplier/derbysoft/full_flow_test.go:60`
- **任务**: 补充完整流程测试
- **描述**: 后续可补充 Book、QueryOrder、Cancel 等完整流程的测试
- **状态**: 待补充

## 任务统计

- **总计**: 45 个 TODO 任务
- **高优先级 (P0)**: 12 个
- **中优先级 (P1)**: 15 个  
- **低优先级 (P2)**: 18 个

## 建议

1. **优先处理高优先级任务**，特别是影响核心功能的 TODO
2. **按模块分组处理**，避免在多个模块间频繁切换
3. **建立任务跟踪机制**，定期更新任务状态
4. **代码审查时关注 TODO**，避免新增 TODO 而不处理
5. **考虑自动化测试**，确保 TODO 完成后功能正常

## 更新记录

- **2024-12-19**: 初始版本，扫描全代码库并记录所有 TODO 任务