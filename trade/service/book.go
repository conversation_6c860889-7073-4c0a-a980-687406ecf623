package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/bytedance/sonic"
	pkgerr "github.com/pkg/errors"

	ctxutil "hotel/common/context"
	"hotel/common/idgen"
	"hotel/common/log"
	"hotel/common/types"
	supplierDomain "hotel/supplier/domain"
	"hotel/trade/dao"
	"hotel/trade/domain"
	"hotel/trade/protocol"
)

// Book
// @desc: Place an order.
// @tags: openapi,booking.hotelbyte.com/trade
// @path: /book
func (s *TradeService) Book(ctx context.Context, req *protocol.BookReq) (*protocol.BookResp, error) {
	// 验证请求参数
	if req.RatePkgId == "" {
		return nil, errors.New("ratePkgId is required")
	}
	if len(req.Guests) == 0 {
		return nil, errors.New("guests information is required")
	}
	orderId := idgen.MustNextInt64ID().Int64()
	if req.PlatformOrderId == 0 {
		req.PlatformOrderId = orderId
	}

	ss, err := s.sessionService.GetSession(ctx, req.SessionId)
	if err != nil {
		return nil, pkgerr.Wrap(err, "failed to get session")
	}
	checkAvailResp, err := s.sessionService.GetCheckAvailResp(ctx, ss)
	if err != nil {
		return nil, pkgerr.Wrap(err, "failed to unmarshal checkAvailResp")
	}
	log.Infoc(ctx, "Parsed ratePkgInfo: %+v", checkAvailResp.RoomRatePkg)

	sellerPayloads, err := s.sessionService.GetSellerPayloads(ctx, ss)
	if err != nil {
		return nil, pkgerr.Wrap(err, "failed to unmarshal sellerPayloads")
	}
	sellerPayload := sellerPayloads.GetSupplierPayload(checkAvailResp.Supplier)
	sellerPayload.Book = &req.BookReq
	// 准备订单业务信息
	bizInfo := &domain.OrderBizInfo{
		CheckAvailResp:      checkAvailResp,
		SellerInputPayloads: sellerPayload,
	}

	// 计算订单价格
	priceCalculator := domain.NewPriceCalculator()
	priceInput := &domain.PriceCalculationInput{
		RatePkgInfo:  checkAvailResp.RoomRatePkg,
		Operator:     req.Operator,
		ExchangeRate: 1.0, // 暂时不使用汇率转换
	}

	priceResult, err := priceCalculator.CalculatePrice(priceInput)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate price: %w", err)
	}

	// 验证价格计算结果
	if err := priceCalculator.ValidatePrice(priceResult); err != nil {
		return nil, fmt.Errorf("invalid price calculation: %w", err)
	}

	log.Infoc(ctx, "Price calculation result: %+v", priceResult)

	// 创建订单记录
	order := &domain.Order{
		Id:           types.ID(orderId),
		ReferenceNo:  req.ReferenceNo,
		Status:       domain.OrderStateCreated,
		CreateTime:   time.Now(),
		OrderAccount: &domain.OrderAccount{
			// 账户信息可以从 req.Operator 中获取
		},
		BizInfo: bizInfo,
	}

	// 2B 不需要支付
	order.Status = domain.OrderStatePaid

	// 使用事务确保数据一致性
	// todo: retry: db save
	err = s.createOrderWithTransaction(ctx, order, checkAvailResp.RoomRatePkg, priceResult)
	if err != nil {
		return nil, fmt.Errorf("failed to create order with transaction: %w", err)
	}

	// ss.SetBookeq(ctx, utils.ToJSON(req))
	_ = s.sessionService.CacheSession(ctx, req.SessionId, ss)

	// 这里应该发送到消息队列，暂时直接处理
	// 发送预订消息到队列进行异步处理
	bookMsg, err := sonic.Marshal(order)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal order: %w", err)
	}

	//todo: 改成消息队列
	go func() {
		if err := s.handleBookMessage(ctxutil.Detach(ctx), bookMsg); err != nil {
			log.Errorc(ctx, "handleBookMessage failed: %v", err)
		}
	}()

	// 基于BIOrder的简化订单创建追踪
	_ = s.fan.Do(ctx, func(ctx context.Context) {
		// 记录订单创建关键指标到日志，后续可基于BIOrder扩展
		log.Infoc(ctx, "Order creation metrics: order_id=%d, supplier=%s, amount=%.2f, check_in=%s, check_out=%s, guests=%d",
			orderId, checkAvailResp.Supplier.GetEnglishName(),
			float64(priceResult.BuyerAmount),
			checkAvailResp.RoomRatePkg.CheckIn.Format("2006-01-02"),
			checkAvailResp.RoomRatePkg.CheckOut.Format("2006-01-02"),
			len(req.Guests))
		//todo: 发送消息给 bi 模块，bi 模块做订单数据更新，实现订单数据同步
	})

	// 返回预订响应
	return &protocol.BookResp{
		PlatformOrderId: orderId,
		BookResp: supplierDomain.BookResp{
			SupplierOrderId: fmt.Sprintf("HB%d", orderId),
			OrderStatus:     supplierDomain.OrderStatus_Submitted,
		},
	}, nil
}

// createSubOrders 创建子订单记录
func (s *TradeService) createSubOrders(ctx context.Context, orderId int64, ratePkgInfo *supplierDomain.RoomRatePkg, priceResult *domain.PriceCalculationResult) error {
	// 计算住宿天数
	nights := domain.CalculateTotalNights(ratePkgInfo.CheckIn.Int64(), ratePkgInfo.CheckOut.Int64())
	if nights <= 0 {
		nights = 1 // 至少一晚
	}

	// 计算每晚的平均价格
	priceCalculator := domain.NewPriceCalculator()
	avgBuyerAmount := priceCalculator.CalculateAverageNightlyRate(priceResult.BuyerAmount, nights)
	avgSellerAmount := priceCalculator.CalculateAverageNightlyRate(priceResult.SellerAmount, nights)

	// 为每一晚创建子订单
	currentDate := ratePkgInfo.CheckIn
	for i := 0; i < nights; i++ {
		//todo: use new struct SubOrderBizInfo
		subOrderBizInfo := map[string]interface{}{
			"nightIndex":  i + 1,
			"totalNights": nights,
			"ratePkgInfo": ratePkgInfo,
		}
		bizInfoJson, _ := json.Marshal(subOrderBizInfo)

		subOrder := &dao.SubOrder{
			OrderId:            uint64(orderId),
			Date:               int64(currentDate.Int64()),
			Status:             0, // 初始状态
			BuyerAmount:        avgBuyerAmount,
			SellerAmount:       avgSellerAmount,
			BuyerRefundAmount:  0, // 初始无退款
			SellerRefundAmount: 0, // 初始无退款
			RatePkgId:          ratePkgInfo.RatePkgId,
			BizInfo:            string(bizInfoJson),
			CreateTime:         time.Now(),
			UpdateTime:         time.Now(),
		}

		_, err := s.orderDao.SubOrder.Insert(ctx, subOrder)
		if err != nil {
			return fmt.Errorf("failed to create sub order for date %d: %w", currentDate.Int64(), err)
		}

		// 移动到下一天
		// 将 DateInt 转换为时间，加一天，再转回 DateInt
		dateStr := currentDate.Format("2006-01-02")
		t, _ := time.Parse("2006-01-02", dateStr)
		nextDay := t.AddDate(0, 0, 1)
		currentDate = types.NewDateIntFromTime(nextDay)
	}

	log.Infoc(ctx, "Created %d sub orders for order %d", nights, orderId)
	return nil
}

// createOrderWithTransaction 在事务中创建订单和子订单
func (s *TradeService) createOrderWithTransaction(ctx context.Context, order *domain.Order, ratePkgInfo *supplierDomain.RoomRatePkg, priceResult *domain.PriceCalculationResult) error {
	// 注意：这里需要实际的事务支持，暂时使用简单的错误处理
	// 在实际项目中，应该使用数据库事务或分布式事务

	// 1. 插入主订单
	err := s.orderDao.Insert(ctx, order)
	if err != nil {
		return fmt.Errorf("failed to create order: %w", err)
	}

	// 2. 获取插入的订单ID
	orderId := int64(order.Id)

	// 3. 创建子订单记录
	if err := s.createSubOrders(ctx, orderId, ratePkgInfo, priceResult); err != nil {
		// 如果子订单创建失败，应该回滚主订单
		// 这里暂时只记录错误，实际应该实现回滚逻辑
		log.Errorc(ctx, "Failed to create sub orders, should rollback main order: %v", err)

		// 尝试删除已创建的主订单（简单的回滚）
		if deleteErr := s.rollbackOrder(ctx, uint64(orderId)); deleteErr != nil {
			log.Errorc(ctx, "Failed to rollback order %d: %v", orderId, deleteErr)
		}

		return fmt.Errorf("failed to create sub orders: %w", err)
	}

	log.Infoc(ctx, "Successfully created order %d with sub orders", orderId)
	return nil
}

// rollbackOrder 回滚订单（简单实现）
func (s *TradeService) rollbackOrder(ctx context.Context, orderId uint64) error {
	// 在实际项目中，这里应该实现更完善的回滚逻辑
	// 包括删除相关的子订单、供应商订单等

	// 1. 删除子订单
	// 注意：这里需要实际的删除方法，暂时跳过
	log.Warnc(ctx, "Rollback: should delete sub orders for order %d", orderId)

	// 2. 删除主订单
	// 注意：这里需要实际的删除方法，暂时跳过
	log.Warnc(ctx, "Rollback: should delete main order %d", orderId)

	return nil
}
