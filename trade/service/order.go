package service

import (
	"context"
	"fmt"
	"hotel/common/types"
	"strconv"

	"hotel/common/bff"
	"hotel/common/i18n"
	"hotel/common/log"
	"hotel/common/pagehelper"
	geographyDomain "hotel/geography/domain"
	tradeDomain "hotel/trade/domain"
	"hotel/trade/protocol"
)

// listOrderCore 实现订单列表查询的核心逻辑
func (s *TradeService) listOrderCore(ctx context.Context, req *protocol.ListOrderReq) (*protocol.ListOrderResp, error) {
	// 处理关键字搜索 - 如果有关键字，尝试解析为订单ID或外部订单号
	if req.InternalKeyword != "" {
		// 尝试将关键字解析为订单ID
		if id, err := strconv.ParseInt(req.InternalKeyword, 10, 64); err == nil {
			req.PlatformOrderIds = append(req.PlatformOrderIds, types.ID(id))
		} else {
			// 如果不是数字，当作外部订单号处理
			req.ReferenceNos = append(req.ReferenceNos, req.InternalKeyword)
		}
	}

	// 构建查询请求
	queryReq := &protocol.QueryOrdersReq{
		QueryOrderCriteria: req.QueryOrderCriteria,
	}

	// 如果没有任何查询条件，设置默认查询条件以避免查询所有数据
	if len(req.PlatformOrderIds) == 0 && len(req.ReferenceNos) == 0 &&
		len(req.StatusList) == 0 && req.CheckInTimeWindow.Start.IsZero() &&
		req.CheckOutTimeWindow.Start.IsZero() && req.CreateTimeWindow.Start.IsZero() &&
		req.CancelTimeWindow.Start.IsZero() {
		// 设置默认状态过滤，只查询已确认的订单
		queryReq.StatusList = []tradeDomain.OrderStatus{tradeDomain.OrderStateConfirmed}
	}

	// 为了提高性能，我们直接在 DAO 层进行分页查询
	// 转换查询条件并添加分页信息
	criteria := convertProtocolCriteriaToDAO(queryReq.QueryOrderCriteria)

	// 设置分页参数
	offset := req.PageReq.GetOffset()
	limit := req.PageReq.PageSize
	if limit <= 0 {
		limit = 20 // 默认每页20条
	}
	criteria.Offset = offset
	criteria.Limit = limit

	// 查询订单数据（带分页）
	pagedOrders, err := s.orderDao.FindByCriteria(ctx, criteria)
	if err != nil {
		return nil, err
	}

	// 查询总数
	total, err := s.orderDao.CountByCriteria(ctx, criteria)
	if err != nil {
		return nil, err
	}

	// 转换为 BFF 格式
	var rows []*bff.ElementRow[tradeDomain.Order]
	for _, order := range pagedOrders {
		rows = append(rows, &bff.ElementRow[tradeDomain.Order]{
			Raw: order,
			Key: order.Id.String(),
		})
	}

	return &protocol.ListOrderResp{
		Table: bff.Table[tradeDomain.Order]{
			PageResp: pagehelper.PageResp{
				Total:   total,
				HasMore: offset+limit < total, // 判断是否还有更多数据
			},
			Rows: rows,
		},
	}, nil
}

// OrderHomeFunction
// @path: /orderHomeFunction
// @tags: admin.hotelbyte.com/trade
func (s *TenantService) OrderHomeFunction(ctx context.Context, req *protocol.OrderHomeFunctionReq) (*protocol.OrderHomeFunctionResp, error) {
	return s.TradeService.orderHomeCore(ctx, req)
}

// OrderHomeFunction
// @path: /orderHomeFunction
// @tags: booking.hotelbyte.com/trade
func (s *CustomerService) OrderHomeFunction(ctx context.Context, req *protocol.OrderHomeFunctionReq) (*protocol.OrderHomeFunctionResp, error) {
	return s.TradeService.orderHomeCore(ctx, req)
}

// ListOrder
// @path: /listOrder
// @tags: admin.hotelbyte.com/trade
func (s *TenantService) ListOrder(ctx context.Context, req *protocol.ListOrderReq) (*protocol.ListOrderResp, error) {
	return s.TradeService.listOrderCore(ctx, req)
}

// ListOrder
// @path: /listOrder
// @tags: booking.hotelbyte.com/trade
func (s *CustomerService) ListOrder(ctx context.Context, req *protocol.ListOrderReq) (*protocol.ListOrderResp, error) {
	return s.TradeService.listOrderCore(ctx, req)
}

// detailOrderCore 实现订单详情查询的核心逻辑
func (s *TradeService) detailOrderCore(ctx context.Context, req *protocol.DetailOrderReq) (*protocol.DetailOrderResp, error) {
	// 查询订单基本信息
	order, err := s.orderDao.GetByID(ctx, req.OrderId)
	if err != nil {
		return nil, err
	}

	summary := &tradeDomain.OrderSummary{
		Id:          order.Id,
		ReferenceNo: order.ReferenceNo,
		Status:      order.Status,
		CreateTime:  order.CreateTime,
		// 其他字段根据需要填充
	}

	// 构建账户信息
	account := &protocol.DetailOrderAccount{
		OrderAccount: &tradeDomain.OrderAccount{
			//BuyerCurrency:            order.BuyerCurrency,
			//BuyerAmount:              order.BuyerAmount,
			//SellerCurrency:           order.SellerCurrency,
			//SellerAmount:             order.SellerAmount,
			//TenantRevenueAmountUsd:   order.TenantRevenueAmountUsd,
			//CustomerRevenueAmountUsd: order.CustomerRevenueAmountUsd,
		},
	}

	// 查询供应商订单信息
	supplierOrders, err := s.orderDao.FindSupplierOrdersByOrderId(ctx, int64(order.Id))
	if err != nil {
		log.Warnc(ctx, "failed to find supplier orders for order %d: %v", order.Id, err)
		supplierOrders = []tradeDomain.SupplierOrder{}
	}

	// 构建子订单信息
	var subOrders []*protocol.DetailOrderSubOrder
	for _, supplierOrder := range supplierOrders {
		subOrder := &protocol.DetailOrderSubOrder{
			Hotel: &protocol.DetailOrderHotel{
				//HotelId:   0, // 从ratePkgId解析
				//HotelName: "", // 需要查询酒店信息
			},
			Booking: bff.Table[tradeDomain.OrderSummary]{
				Rows: []*bff.ElementRow[tradeDomain.OrderSummary]{
					{
						//Raw: summary,
						Key: summary.Id.String(),
					},
				},
			},
			Gain: bff.Table[tradeDomain.OrderGainSummary]{
				Rows: []*bff.ElementRow[tradeDomain.OrderGainSummary]{
					{
						//Raw: &tradeDomain.OrderGainSummary{
						//	PlatformRevenue: order.PlatformRevenueAmountUsd,
						//	TenantRevenue:   order.TenantRevenueAmountUsd,
						//	CustomerRevenue: order.CustomerRevenueAmountUsd,
						//},
						Key: fmt.Sprintf("gain_%d", supplierOrder.ID),
					},
				},
			},
		}
		subOrders = append(subOrders, subOrder)
	}

	// 如果没有供应商订单，创建一个默认的子订单
	if len(subOrders) == 0 {
		subOrders = append(subOrders, &protocol.DetailOrderSubOrder{
			Hotel: &protocol.DetailOrderHotel{
				//HotelId:   0,
				//HotelName: "Unknown Hotel",
			},
			Booking: bff.Table[tradeDomain.OrderSummary]{
				Rows: []*bff.ElementRow[tradeDomain.OrderSummary]{
					{
						//Raw: summary,
						Key: summary.Id.String(),
					},
				},
			},
			Gain: bff.Table[tradeDomain.OrderGainSummary]{
				Rows: []*bff.ElementRow[tradeDomain.OrderGainSummary]{
					{
						Raw: tradeDomain.OrderGainSummary{
							//TenantRevenue:   order.TenantRevenueAmountUsd,
							//CustomerRevenue: order.CustomerRevenueAmountUsd,
						},
						Key: fmt.Sprintf("gain_%d", order.Id),
					},
				},
			},
		})
	}

	return &protocol.DetailOrderResp{
		Summary:   summary,
		Account:   account,
		SubOrders: subOrders,
	}, nil
}

// DetailOrder
// @path: /detailOrder
// @tags: internal.hotelbyte.com/trade
func (s *TradeService) DetailOrder(ctx context.Context, req *protocol.DetailOrderReq) (*protocol.DetailOrderResp, error) {
	return s.detailOrderCore(ctx, req)
}

// DetailOrder
// @path: /detailOrder
// @tags: admin.hotelbyte.com/trade
func (s *TenantService) DetailOrder(ctx context.Context, req *protocol.DetailOrderReq) (*protocol.DetailOrderResp, error) {
	return s.TradeService.detailOrderCore(ctx, req)
}

// DetailOrder
// @path: /detailOrder
// @tags: booking.hotelbyte.com/trade
func (s *CustomerService) DetailOrder(ctx context.Context, req *protocol.DetailOrderReq) (*protocol.DetailOrderResp, error) {
	return s.TradeService.detailOrderCore(ctx, req)
}

// QueryOrders
// @desc: Query order details by order id, which is given in Book API response (hotel.trade.protocol.BookResp).
// @tags: openapi
// @path: /queryOrders
func (s *TradeService) QueryOrders(ctx context.Context, req *protocol.QueryOrdersReq) (*protocol.QueryOrdersResp, error) {
	return s.queryOrdersCore(ctx, req)
}

// queryOrdersCore 实现订单查询的核心逻辑
func (s *TradeService) queryOrdersCore(ctx context.Context, req *protocol.QueryOrdersReq) (*protocol.QueryOrdersResp, error) {
	// 转换查询条件
	criteria := convertProtocolCriteriaToDAO(req.QueryOrderCriteria)

	// 如果没有任何查询条件，返回空结果
	if len(criteria.PlatformOrderIds) == 0 && len(criteria.ReferenceNos) == 0 &&
		len(criteria.StatusList) == 0 && criteria.CheckInTimeWindow == nil &&
		criteria.CheckOutTimeWindow == nil && criteria.CreateTimeWindow == nil &&
		criteria.CancelTimeWindow == nil {
		return &protocol.QueryOrdersResp{Orders: nil}, nil
	}

	// 查询订单数据
	orders, err := s.orderDao.FindByCriteria(ctx, criteria)
	if err != nil {
		return nil, err
	}

	return &protocol.QueryOrdersResp{Orders: orders}, nil
}

// orderHomeCore 实现订单首页的核心逻辑
func (s *TradeService) orderHomeCore(ctx context.Context, req *protocol.OrderHomeFunctionReq) (*protocol.OrderHomeFunctionResp, error) {
	// 获取标签列表
	tags := []string{"热门", "商务", "度假", "会议", "家庭"}

	// 获取国家订单统计 - 这里应该从数据库查询真实数据
	// 暂时返回模拟数据
	countryOrderCounters := []geographyDomain.RegionCounter{
		{
			RegionId:      1,
			RegionName:    i18n.I18N{Zh: "中国", En: "China"},
			CounterNumber: 1250,
			CounterName:   i18n.I18N{Zh: "订单数", En: "Orders"},
		},
		{
			RegionId:      2,
			RegionName:    i18n.I18N{Zh: "美国", En: "United States"},
			CounterNumber: 856,
			CounterName:   i18n.I18N{Zh: "订单数", En: "Orders"},
		},
		{
			RegionId:      3,
			RegionName:    i18n.I18N{Zh: "日本", En: "Japan"},
			CounterNumber: 432,
			CounterName:   i18n.I18N{Zh: "订单数", En: "Orders"},
		},
	}

	// 获取推荐订单 - 查询最近的一些订单作为推荐
	var recommendedOrders []tradeDomain.Order
	// 这里可以根据业务逻辑查询推荐订单
	// 暂时返回空列表

	// 转换为 BFF 格式
	var rows []*bff.ElementRow[tradeDomain.Order]
	for _, order := range recommendedOrders {
		rows = append(rows, &bff.ElementRow[tradeDomain.Order]{
			Raw: order,
			Key: order.Id.String(),
		})
	}

	return &protocol.OrderHomeFunctionResp{
		Tags:                 tags,
		CountryOrderCounters: countryOrderCounters,
		RecommendedOrders: bff.Table[tradeDomain.Order]{
			PageResp: pagehelper.PageResp{
				Total: int64(len(recommendedOrders)),
			},
			Rows: rows,
		},
	}, nil
}
