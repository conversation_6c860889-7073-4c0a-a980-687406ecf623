package service

import (
	"encoding/json"
	"time"

	"hotel/common/types"
	supplierDomain "hotel/supplier/domain"
	"hotel/trade/dao"
	tradeDomain "hotel/trade/domain"
	"hotel/trade/protocol"
)

// convertProtocolCriteriaToDAO 将 protocol 的查询条件转换为 DAO 的查询条件
func convertProtocolCriteriaToDAO(criteria protocol.QueryOrderCriteria) *dao.OrderQueryCriteria {
	daoCriteria := &dao.OrderQueryCriteria{}

	// 转换平台订单ID
	if len(criteria.PlatformOrderIds) > 0 {
		for _, id := range criteria.PlatformOrderIds {
			daoCriteria.PlatformOrderIds = append(daoCriteria.PlatformOrderIds, uint64(id))
		}
	}

	// 转换外部订单号
	daoCriteria.ReferenceNos = criteria.ReferenceNos

	// 转换时间窗口 - 检查是否为零值
	if !criteria.CheckInTimeWindow.Start.IsZero() || !criteria.CheckInTimeWindow.End.IsZero() {
		daoCriteria.CheckInTimeWindow = &types.TimeWindow{}
		if !criteria.CheckInTimeWindow.Start.IsZero() {
			daoCriteria.CheckInTimeWindow.Start = criteria.CheckInTimeWindow.Start
		}
		if !criteria.CheckInTimeWindow.End.IsZero() {
			daoCriteria.CheckInTimeWindow.End = criteria.CheckInTimeWindow.End
		}
	}

	if !criteria.CheckOutTimeWindow.Start.IsZero() || !criteria.CheckOutTimeWindow.End.IsZero() {
		daoCriteria.CheckOutTimeWindow = &types.TimeWindow{}
		if !criteria.CheckOutTimeWindow.Start.IsZero() {
			daoCriteria.CheckOutTimeWindow.Start = criteria.CheckOutTimeWindow.Start
		}
		if !criteria.CheckOutTimeWindow.End.IsZero() {
			daoCriteria.CheckOutTimeWindow.End = criteria.CheckOutTimeWindow.End
		}
	}

	if !criteria.CreateTimeWindow.Start.IsZero() || !criteria.CreateTimeWindow.End.IsZero() {
		daoCriteria.CreateTimeWindow = &types.TimeWindow{}
		if !criteria.CreateTimeWindow.Start.IsZero() {
			daoCriteria.CreateTimeWindow.Start = criteria.CreateTimeWindow.Start
		}
		if !criteria.CreateTimeWindow.End.IsZero() {
			daoCriteria.CreateTimeWindow.End = criteria.CreateTimeWindow.End
		}
	}

	if !criteria.CancelTimeWindow.Start.IsZero() || !criteria.CancelTimeWindow.End.IsZero() {
		daoCriteria.CancelTimeWindow = &types.TimeWindow{}
		if !criteria.CancelTimeWindow.Start.IsZero() {
			daoCriteria.CancelTimeWindow.Start = criteria.CancelTimeWindow.Start
		}
		if !criteria.CancelTimeWindow.End.IsZero() {
			daoCriteria.CancelTimeWindow.End = criteria.CancelTimeWindow.End
		}
	}

	// 转换状态列表
	if len(criteria.StatusList) > 0 {
		for _, status := range criteria.StatusList {
			daoCriteria.StatusList = append(daoCriteria.StatusList, int64(status))
		}
	}

	// 转换标签 (暂时忽略，因为数据库表中可能没有标签字段)
	daoCriteria.Tags = criteria.Tags

	return daoCriteria
}

// 注意：转换函数已移至 dao 层，service 层不再直接处理 model 到 domain 的转换

// extractRoomInfoFromBizInfo 从 BizInfo 中提取房间信息
func extractRoomInfoFromBizInfo(bizInfo *tradeDomain.OrderBizInfo) tradeDomain.OrderRoom {
	if bizInfo == nil || bizInfo.SellerInputPayloads == nil || bizInfo.SellerInputPayloads.Book == nil {
		return tradeDomain.OrderRoom{
			RoomIndex: 1,
			Guests:    []*supplierDomain.Guest{},
		}
	}

	// 从 Book 请求中提取客人信息
	guests := bizInfo.SellerInputPayloads.Book.Guests

	return tradeDomain.OrderRoom{
		RoomIndex:  1, // 默认第一个房间
		Guests:     convertGuestsToPointers(guests),
		RefundInfo: []*tradeDomain.OrderRoomRefundInfo{}, // 初始为空
	}
}

// convertGuestsToPointers 将 Guest slice 转换为指针 slice
func convertGuestsToPointers(guests []supplierDomain.Guest) []*supplierDomain.Guest {
	var result []*supplierDomain.Guest
	for i := range guests {
		result = append(result, &guests[i])
	}
	return result
}

// extractGuestInfoFromBizInfo 从 BizInfo 中提取客人信息
func extractGuestInfoFromBizInfo(bizInfo *tradeDomain.OrderBizInfo) (adultsCount, childrenCount int) {
	if bizInfo == nil || bizInfo.SellerInputPayloads == nil || bizInfo.SellerInputPayloads.Book == nil {
		return 0, 0
	}

	guests := bizInfo.SellerInputPayloads.Book.Guests
	for _, guest := range guests {
		if guest.Age >= 18 { // 假设18岁以上为成人
			adultsCount++
		} else {
			childrenCount++
		}
	}

	// 如果没有年龄信息，假设都是成人
	if adultsCount == 0 && childrenCount == 0 {
		adultsCount = len(guests)
	}

	return adultsCount, childrenCount
}

// extractBookerInfoFromBizInfo 从 BizInfo 中提取预订人信息
func extractBookerInfoFromBizInfo(bizInfo *tradeDomain.OrderBizInfo) supplierDomain.Booker {
	if bizInfo == nil || bizInfo.SellerInputPayloads == nil || bizInfo.SellerInputPayloads.Book == nil {
		return supplierDomain.Booker{}
	}

	return bizInfo.SellerInputPayloads.Book.Booker
}

// calculateNightsFromDates 根据入住和退房日期计算住宿晚数
func calculateNightsFromDates(checkIn, checkOut int64) int {
	if checkIn == 0 || checkOut == 0 {
		return 1 // 默认1晚
	}

	// 将 int64 格式的日期转换为时间
	checkInTime, err := time.Parse("20060102", types.DateInt(checkIn).Format(""))
	if err != nil {
		return 1
	}

	checkOutTime, err := time.Parse("20060102", types.DateInt(checkOut).Format(""))
	if err != nil {
		return 1
	}

	duration := checkOutTime.Sub(checkInTime)
	nights := int(duration.Hours() / 24)

	if nights <= 0 {
		return 1 // 最少1晚
	}

	return nights
}

// convertDAOOrdersToDomain 将 DAO 的 Order slice 转换为 Domain 的 Order slice
func convertDAOOrdersToDomain(daoOrders []*dao.Order) []tradeDomain.Order {
	var domainOrders []tradeDomain.Order
	for _, daoOrder := range daoOrders {
		domainOrders = append(domainOrders, convertDAOOrderToDomain(daoOrder))
	}
	return domainOrders
}

// convertDAOOrderToDomain 将 DAO 的 Order 转换为 Domain 的 Order
func convertDAOOrderToDomain(daoOrder *dao.Order) tradeDomain.Order {
	// 状态映射
	var status tradeDomain.OrderStatus = tradeDomain.OrderStatus(0)

	switch daoOrder.Status {
	case int64(tradeDomain.OrderStateCreated):
		status = tradeDomain.OrderStateCreated
	case int64(tradeDomain.OrderStateConfirmed):
		status = tradeDomain.OrderStateConfirmed
	case int64(tradeDomain.OrderStateCompleted):
		status = tradeDomain.OrderStateCompleted
	case int64(tradeDomain.OrderStateCancelled):
		status = tradeDomain.OrderStateCancelled
	case int64(tradeDomain.OrderStatePaid):
		status = tradeDomain.OrderStatePaid
	case int64(tradeDomain.OrderStateNeedSupplierConfirmed):
		status = tradeDomain.OrderStateNeedSupplierConfirmed
	case int64(tradeDomain.OrderStateNeedCancel):
		status = tradeDomain.OrderStateNeedCancel
	case int64(tradeDomain.OrderStateNeedRefund):
		status = tradeDomain.OrderStateNeedRefund
	}

	// 解析 BizInfo 获取详细信息
	bizInfo := parseBizInfo(daoOrder.BizInfo)

	// 提取房间信息
	roomInfo := extractRoomInfoFromBizInfo(bizInfo)
	bookerInfo := extractBookerInfoFromBizInfo(bizInfo)

	return tradeDomain.Order{
		Id:            types.ID(daoOrder.Id),
		ReferenceNo:   daoOrder.ReferenceNo,
		Status:        status,
		CreateTime:    daoOrder.CreateTime,
		ConfirmNumber: "", // 将在后续从供应商订单中获取
		Rooms:         []tradeDomain.OrderRoom{roomInfo},
		Booker:        bookerInfo,
		Tags:          []string{}, // 可以从 BizInfo 中提取
		OrderAccount:  &tradeDomain.OrderAccount{
			// OrderAccount 结构与预期不同，暂时设为 nil
			// 实际的账户信息可能需要从其他地方获取
		},
		BizInfo: bizInfo,
	}
}
