package dao

import (
	"context"
	"encoding/json"
	"hotel/common/types"
	supplierDomain "hotel/supplier/domain"
	"hotel/trade/domain"

	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

type OrderDao struct {
	Order          *OrderModel
	SupplierOrder  *SupplierOrderModel
	SubOrder       SubOrderModel
	RefundOrder    RefundOrderModel
	RefundSubOrder RefundSubOrderModel
	Redis          *redis.Redis
}

func NewOrderDao(mysqlDSN string, redisCfg redis.RedisConf) *OrderDao {
	conn := sqlx.NewSqlConn("MYSQL", mysqlDSN)
	redisCli, err := redis.NewRedis(redisCfg)
	if err != nil {
		panic("redis Init error: " + err.Error())
	}
	return &OrderDao{
		Order:          NewOrderModel(conn),
		SupplierOrder:  NewSupplierOrderModel(conn),
		SubOrder:       NewSubOrderModel(conn),
		RefundOrder:    NewRefundOrderModel(conn),
		RefundSubOrder: NewRefundSubOrderModel(conn),
		Redis:          redisCli,
	}
}

// FindByCriteria 根据查询条件查找订单，返回 domain.Order 对象
func (d *OrderDao) FindByCriteria(ctx context.Context, criteria *OrderQueryCriteria) ([]domain.Order, error) {
	daoOrders, err := d.Order.FindByCriteria(ctx, criteria)
	if err != nil {
		return nil, err
	}

	var domainOrders []domain.Order
	for _, daoOrder := range daoOrders {
		domainOrders = append(domainOrders, convertDAOOrderToDomain(daoOrder))
	}

	return domainOrders, nil
}

// CountByCriteria 根据查询条件统计订单数量
func (d *OrderDao) CountByCriteria(ctx context.Context, criteria *OrderQueryCriteria) (int64, error) {
	return d.Order.CountByCriteria(ctx, criteria)
}

// GetByID 根据ID获取订单，返回 domain.Order 对象
func (d *OrderDao) GetByID(ctx context.Context, id types.ID) (*domain.Order, error) {
	daoOrder, err := d.Order.FindOne(ctx, uint64(id))
	if err != nil {
		return nil, err
	}

	domainOrder := convertDAOOrderToDomain(daoOrder)
	return &domainOrder, nil
}

// GetByReferenceNo 根据参考号获取订单，返回 domain.Order 对象
func (d *OrderDao) GetByReferenceNo(ctx context.Context, referenceNo string) (*domain.Order, error) {
	daoOrder, err := d.Order.FindByReferenceNo(ctx, referenceNo)
	if err != nil {
		return nil, err
	}

	domainOrder := convertDAOOrderToDomain(daoOrder)
	return &domainOrder, nil
}

// Insert 插入订单，接受 domain.Order 对象
func (d *OrderDao) Insert(ctx context.Context, order *domain.Order) error {
	daoOrder := convertDomainOrderToDAO(order)
	_, err := d.Order.Insert(ctx, daoOrder)
	return err
}

// Update 更新订单，接受 domain.Order 对象
func (d *OrderDao) Update(ctx context.Context, order *domain.Order) error {
	daoOrder := convertDomainOrderToDAO(order)
	return d.Order.Update(ctx, daoOrder)
}

// Delete 删除订单
func (d *OrderDao) Delete(ctx context.Context, id types.ID) error {
	return d.Order.Delete(ctx, uint64(id))
}

// FindList 根据状态查找订单列表，返回 domain.Order 对象
func (d *OrderDao) FindList(ctx context.Context, status ...int64) ([]domain.Order, error) {
	daoOrders, err := d.Order.FindList(ctx, status...)
	if err != nil {
		return nil, err
	}

	var domainOrders []domain.Order
	for _, daoOrder := range daoOrders {
		domainOrders = append(domainOrders, convertDAOOrderToDomain(daoOrder))
	}

	return domainOrders, nil
}

// FindListWithPagination 分页查询订单列表，返回 domain.Order 对象
func (d *OrderDao) FindListWithPagination(ctx context.Context, state []int64, offset, limit int64) ([]domain.Order, error) {
	daoOrders, err := d.Order.FindListWithPagination(ctx, state, offset, limit)
	if err != nil {
		return nil, err
	}

	var domainOrders []domain.Order
	for _, daoOrder := range daoOrders {
		domainOrders = append(domainOrders, convertDAOOrderToDomain(daoOrder))
	}

	return domainOrders, nil
}

// FindByIds 根据ID列表查找订单，返回 domain.Order 对象
func (d *OrderDao) FindByIds(ctx context.Context, ids []uint64) ([]domain.Order, error) {
	daoOrders, err := d.Order.FindByIds(ctx, ids)
	if err != nil {
		return nil, err
	}

	var domainOrders []domain.Order
	for _, daoOrder := range daoOrders {
		domainOrders = append(domainOrders, convertDAOOrderToDomain(daoOrder))
	}

	return domainOrders, nil
}

// FindByReferenceNos 根据参考号列表查找订单，返回 domain.Order 对象
func (d *OrderDao) FindByReferenceNos(ctx context.Context, referenceNos []string) ([]domain.Order, error) {
	daoOrders, err := d.Order.FindByReferenceNos(ctx, referenceNos)
	if err != nil {
		return nil, err
	}

	var domainOrders []domain.Order
	for _, daoOrder := range daoOrders {
		domainOrders = append(domainOrders, convertDAOOrderToDomain(daoOrder))
	}

	return domainOrders, nil
}

// UpdateStatus 更新订单状态
func (d *OrderDao) UpdateStatus(ctx context.Context, id types.ID, status domain.OrderStatus) error {
	return d.Order.UpdateStatus(ctx, id, status)
}

// CountByStatus 按状态统计订单数量
func (d *OrderDao) CountByStatus(ctx context.Context, state int64) (int64, error) {
	return d.Order.CountByStatus(ctx, state)
}

// FindSupplierOrdersByOrderId 根据 orderId 查找供应商订单，返回 domain.SupplierOrder 对象
func (d *OrderDao) FindSupplierOrdersByOrderId(ctx context.Context, orderId int64) ([]domain.SupplierOrder, error) {
	daoOrders, err := d.SupplierOrder.FindByOrderId(ctx, orderId)
	if err != nil {
		return nil, err
	}
	var domainOrders []domain.SupplierOrder
	for _, daoOrder := range daoOrders {
		domainOrders = append(domainOrders, convertDAOSupplierOrderToDomain(daoOrder))
	}
	return domainOrders, nil
}

// InsertSupplierOrder 插入供应商订单，接受 domain.SupplierOrder 对象
func (d *OrderDao) InsertSupplierOrder(ctx context.Context, order *domain.SupplierOrder) error {
	daoOrder := convertDomainSupplierOrderToDAO(order)
	_, err := d.SupplierOrder.Insert(ctx, daoOrder)
	return err
}

// UpdateSupplierOrder 更新供应商订单，接受 domain.SupplierOrder 对象
func (d *OrderDao) UpdateSupplierOrder(ctx context.Context, order *domain.SupplierOrder) error {
	daoOrder := convertDomainSupplierOrderToDAO(order)
	return d.SupplierOrder.Update(ctx, daoOrder)
}

// FindRefundOrdersByOrderId 根据 orderId 查找退款订单，返回 domain.RefundOrder 对象
func (d *OrderDao) FindRefundOrdersByOrderId(ctx context.Context, orderId int64) ([]domain.RefundOrder, error) {
	daoOrders, err := d.RefundOrder.FindByOrderId(ctx, orderId)
	if err != nil {
		return nil, err
	}
	var domainOrders []domain.RefundOrder
	for _, daoOrder := range daoOrders {
		domainOrders = append(domainOrders, convertDAORefundOrderToDomain(daoOrder))
	}
	return domainOrders, nil
}

// InsertRefundOrder 插入退款订单，接受 domain.RefundOrder 对象
func (d *OrderDao) InsertRefundOrder(ctx context.Context, order *domain.RefundOrder) error {
	daoOrder := convertDomainRefundOrderToDAO(order)
	_, err := d.RefundOrder.Insert(ctx, daoOrder)
	return err
}

// UpdateRefundOrder 更新退款订单，接受 domain.RefundOrder 对象
func (d *OrderDao) UpdateRefundOrder(ctx context.Context, order *domain.RefundOrder) error {
	daoOrder := convertDomainRefundOrderToDAO(order)
	return d.RefundOrder.Update(ctx, daoOrder)
}

// --- model <-> domain 转换 ---
func convertDAOOrderToDomain(daoOrder *Order) domain.Order {
	// 状态映射
	var status domain.OrderStatus = domain.OrderStatus(0)

	switch daoOrder.Status {
	case int64(domain.OrderStateCreated):
		status = domain.OrderStateCreated
	case int64(domain.OrderStateConfirmed):
		status = domain.OrderStateConfirmed
	case int64(domain.OrderStateCompleted):
		status = domain.OrderStateCompleted
	case int64(domain.OrderStateCancelled):
		status = domain.OrderStateCancelled
	case int64(domain.OrderStatePaid):
		status = domain.OrderStatePaid
	case int64(domain.OrderStateNeedSupplierConfirmed):
		status = domain.OrderStateNeedSupplierConfirmed
	case int64(domain.OrderStateNeedCancel):
		status = domain.OrderStateNeedCancel
	case int64(domain.OrderStateNeedRefund):
		status = domain.OrderStateNeedRefund
	}

	// 解析 BizInfo 获取详细信息
	bizInfo := parseBizInfo(daoOrder.BizInfo)

	// 提取房间信息
	roomInfo := extractRoomInfoFromBizInfo(bizInfo)
	bookerInfo := extractBookerInfoFromBizInfo(bizInfo)

	return domain.Order{
		Id:            types.ID(daoOrder.Id),
		ReferenceNo:   daoOrder.ReferenceNo,
		Status:        status,
		CreateTime:    daoOrder.CreateTime,
		ConfirmNumber: "", // 将在后续从供应商订单中获取
		Rooms:         []domain.OrderRoom{roomInfo},
		Booker:        bookerInfo,
		Tags:          []string{}, // 可以从 BizInfo 中提取
		OrderAccount:  &domain.OrderAccount{
			// OrderAccount 结构与预期不同，暂时设为 nil
			// 实际的账户信息可能需要从其他地方获取
		},
		BizInfo: bizInfo,
	}
}

func convertDomainOrderToDAO(domainOrder *domain.Order) *Order {
	return &Order{
		Id:          uint64(domainOrder.Id),
		ReferenceNo: domainOrder.ReferenceNo,
		Status:      int64(domainOrder.Status),
		CreateTime:  domainOrder.CreateTime,
		BizInfo:     serializeBizInfo(domainOrder.BizInfo),
		// 其他字段根据需要添加
	}
}

func convertDAOSupplierOrderToDomain(daoOrder *SupplierOrder) domain.SupplierOrder {
	return domain.SupplierOrder{
		ID:              types.ID(daoOrder.Id),
		OrderID:         types.ID(daoOrder.OrderId),
		SupplierID:      daoOrder.SupplierId,
		SupplierOrderID: daoOrder.SupplierOrderId,
		Status:          domain.SupplierOrderStatus(daoOrder.Status),
		CreateTime:      daoOrder.CreateTime,
		UpdateTime:      daoOrder.UpdateTime,
		RawData:         daoOrder.RawData,
	}
}

func convertDomainSupplierOrderToDAO(domainOrder *domain.SupplierOrder) *SupplierOrder {
	return &SupplierOrder{
		Id:              uint64(domainOrder.ID),
		OrderId:         uint64(domainOrder.OrderID),
		SupplierId:      domainOrder.SupplierID,
		SupplierOrderId: domainOrder.SupplierOrderID,
		Status:          int64(domainOrder.Status),
		CreateTime:      domainOrder.CreateTime,
		UpdateTime:      domainOrder.UpdateTime,
		RawData:         domainOrder.RawData,
	}
}

func convertDAORefundOrderToDomain(daoOrder *RefundOrder) domain.RefundOrder {
	return domain.RefundOrder{
		ID:              types.ID(daoOrder.Id),
		OrderID:         types.ID(daoOrder.OrderId),
		RefundAmount:    daoOrder.RefundAmount,
		RefundReason:    daoOrder.RefundReason,
		Status:          domain.RefundOrderStatus(daoOrder.Status),
		CreateTime:      daoOrder.CreateTime,
		UpdateTime:      daoOrder.UpdateTime,
		ProcessTime:     daoOrder.ProcessTime,
		CompleteTime:    daoOrder.CompleteTime,
		OperatorID:      (*types.ID)(daoOrder.OperatorId),
		OperatorName:    daoOrder.OperatorName,
		RefundMethod:    daoOrder.RefundMethod,
		RefundAccount:   daoOrder.RefundAccount,
		RefundReference: daoOrder.RefundReference,
		Remark:          daoOrder.Remark,
	}
}

func convertDomainRefundOrderToDAO(domainOrder *domain.RefundOrder) *RefundOrder {
	return &RefundOrder{
		Id:              uint64(domainOrder.ID),
		OrderId:         uint64(domainOrder.OrderID),
		RefundAmount:    domainOrder.RefundAmount,
		RefundReason:    domainOrder.RefundReason,
		Status:          int64(domainOrder.Status),
		CreateTime:      domainOrder.CreateTime,
		UpdateTime:      domainOrder.UpdateTime,
		ProcessTime:     domainOrder.ProcessTime,
		CompleteTime:    domainOrder.CompleteTime,
		OperatorId:      (*uint64)(domainOrder.OperatorID),
		OperatorName:    domainOrder.OperatorName,
		RefundMethod:    domainOrder.RefundMethod,
		RefundAccount:   domainOrder.RefundAccount,
		RefundReference: domainOrder.RefundReference,
		Remark:          domainOrder.Remark,
	}
}

// parseBizInfo 解析订单的 BizInfo JSON 字段
func parseBizInfo(bizInfoJson string) *domain.OrderBizInfo {
	if bizInfoJson == "" {
		return nil
	}

	var bizInfo domain.OrderBizInfo
	if err := json.Unmarshal([]byte(bizInfoJson), &bizInfo); err != nil {
		// 如果解析失败，返回 nil，不影响其他字段的显示
		return nil
	}

	return &bizInfo
}

// serializeBizInfo 序列化 BizInfo 为 JSON 字符串
func serializeBizInfo(bizInfo *domain.OrderBizInfo) string {
	if bizInfo == nil {
		return ""
	}

	data, err := json.Marshal(bizInfo)
	if err != nil {
		return ""
	}

	return string(data)
}

// extractRoomInfoFromBizInfo 从 BizInfo 中提取房间信息
func extractRoomInfoFromBizInfo(bizInfo *domain.OrderBizInfo) domain.OrderRoom {
	if bizInfo == nil || bizInfo.SellerInputPayloads == nil || bizInfo.SellerInputPayloads.Book == nil {
		return domain.OrderRoom{
			RoomIndex: 1,
			Guests:    []*supplierDomain.Guest{},
		}
	}

	// 从 Book 请求中提取客人信息
	guests := bizInfo.SellerInputPayloads.Book.Guests

	return domain.OrderRoom{
		RoomIndex:  1, // 默认第一个房间
		Guests:     convertGuestsToPointers(guests),
		RefundInfo: []*domain.OrderRoomRefundInfo{}, // 初始为空
	}
}

// convertGuestsToPointers 将 Guest slice 转换为指针 slice
func convertGuestsToPointers(guests []supplierDomain.Guest) []*supplierDomain.Guest {
	var result []*supplierDomain.Guest
	for i := range guests {
		result = append(result, &guests[i])
	}
	return result
}

// extractBookerInfoFromBizInfo 从 BizInfo 中提取预订人信息
func extractBookerInfoFromBizInfo(bizInfo *domain.OrderBizInfo) supplierDomain.Booker {
	if bizInfo == nil || bizInfo.SellerInputPayloads == nil || bizInfo.SellerInputPayloads.Book == nil {
		return supplierDomain.Booker{}
	}

	return bizInfo.SellerInputPayloads.Book.Booker
}
