package dao

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"hotel/common/money"
	"hotel/common/types"
	"hotel/trade/domain"
)

// OrderDao 订单数据访问对象
type OrderDao struct {
	orderModel       OrderModel
	supplierOrderModel SupplierOrderModel
	refundOrderModel RefundOrderModel
}

// NewOrderDao 创建新的订单DAO
func NewOrderDao(orderModel OrderModel, supplierOrderModel SupplierOrderModel, refundOrderModel RefundOrderModel) *OrderDao {
	return &OrderDao{
		orderModel:         orderModel,
		supplierOrderModel: supplierOrderModel,
		refundOrderModel:   refundOrderModel,
	}
}

// FindByCriteria 根据条件查询订单
func (d *OrderDao) FindByCriteria(ctx context.Context, criteria OrderQueryCriteria) ([]*domain.Order, error) {
	daoOrders, err := d.orderModel.FindByCriteria(ctx, criteria)
	if err != nil {
		return nil, err
	}

	var domainOrders []*domain.Order
	for _, daoOrder := range daoOrders {
		domainOrder := convertDAOOrderToDomain(daoOrder)
		domainOrders = append(domainOrders, &domainOrder)
	}

	return domainOrders, nil
}

// GetByID 根据ID获取订单
func (d *OrderDao) GetByID(ctx context.Context, id types.ID) (*domain.Order, error) {
	daoOrder, err := d.orderModel.FindOne(ctx, uint64(id))
	if err != nil {
		return nil, err
	}

	domainOrder := convertDAOOrderToDomain(daoOrder)
	return &domainOrder, nil
}

// Insert 插入新订单
func (d *OrderDao) Insert(ctx context.Context, order *domain.Order) error {
	daoOrder := convertDomainOrderToDAO(*order)
	_, err := d.orderModel.Insert(ctx, daoOrder)
	return err
}

// Update 更新订单
func (d *OrderDao) Update(ctx context.Context, order *domain.Order) error {
	daoOrder := convertDomainOrderToDAO(*order)
	return d.orderModel.Update(ctx, daoOrder)
}

// FindSupplierOrdersByOrderId 根据订单ID查找供应商订单
func (d *OrderDao) FindSupplierOrdersByOrderId(ctx context.Context, orderId types.ID) ([]*domain.SupplierOrder, error) {
	// 这里需要实现具体的查询逻辑
	// 暂时返回空切片
	return []*domain.SupplierOrder{}, nil
}

// --- model <-> domain 转换 ---
// convertDAOOrderToDomain 将 DAO 层的 Order 转换为 Domain 层的 Order
func convertDAOOrderToDomain(daoOrder *Order) domain.Order {
	// 状态映射
	status := domain.OrderStatus(daoOrder.Status)

	// 解析 BizInfo
	bizInfo := parseBizInfo(daoOrder.BizInfo)

	return domain.Order{
		Id:            types.ID(daoOrder.Id),
		ReferenceNo:   daoOrder.ReferenceNo,
		Status:        status,
		CreateTime:    daoOrder.CreateTime,
		ConfirmNumber: daoOrder.ConfirmNumber,
		Rooms:         extractRoomInfoFromBizInfo(bizInfo),
		Booker:        extractBookerInfoFromBizInfo(bizInfo),
		Tags:          bizInfo.Tags,
		BizInfo:       bizInfo,
	}
}

func convertDomainOrderToDAO(domainOrder domain.Order) *Order {
	// 序列化 BizInfo
	bizInfoStr := serializeBizInfo(domainOrder.BizInfo)

	return &Order{
		Id:            uint64(domainOrder.Id),
		ReferenceNo:   domainOrder.ReferenceNo,
		Status:        int64(domainOrder.Status),
		CreateTime:    domainOrder.CreateTime,
		UpdateTime:    time.Now(),
		ConfirmNumber: domainOrder.ConfirmNumber,
		BizInfo:       bizInfoStr,
	}
}

// convertDAOSupplierOrderToDomain 将 DAO 层的 SupplierOrder 转换为 Domain 层的 SupplierOrder
func convertDAOSupplierOrderToDomain(daoOrder *SupplierOrder) domain.SupplierOrder {
	return domain.SupplierOrder{
		ID:              types.ID(daoOrder.Id),
		OrderID:         types.ID(daoOrder.OrderId),
		SupplierID:      fmt.Sprintf("%d", daoOrder.SupplierId),
		SupplierOrderID: daoOrder.SupplierOrderId,
		Status:          domain.SupplierOrderStatus(daoOrder.Status),
		RawData:         daoOrder.BizInfo,
		CreateTime:      daoOrder.CreateTime,
		UpdateTime:      daoOrder.UpdateTime,
	}
}

func convertDomainSupplierOrderToDAO(domainOrder domain.SupplierOrder) *SupplierOrder {
	supplierId, _ := strconv.ParseInt(domainOrder.SupplierID, 10, 64)
	return &SupplierOrder{
		Id:              uint64(domainOrder.ID),
		OrderId:         int64(domainOrder.OrderID),
		SupplierId:      supplierId,
		SupplierOrderId: domainOrder.SupplierOrderID,
		Status:          int64(domainOrder.Status),
		BizInfo:         domainOrder.RawData,
		CreateTime:      domainOrder.CreateTime,
		UpdateTime:      domainOrder.UpdateTime,
	}
}

// convertDAORefundOrderToDomain 将 DAO 层的 RefundOrder 转换为 Domain 层的 RefundOrder
func convertDAORefundOrderToDomain(daoOrder *RefundOrder) domain.RefundOrder {
	return domain.RefundOrder{
		ID:           types.ID(daoOrder.Id),
		OrderID:      types.ID(daoOrder.OrderId),
		RefundAmount: money.NewMoney(daoOrder.BuyerRefundAmount, "CNY"),
		Status:       domain.RefundOrderStatus(daoOrder.Status),
		CreateTime:   daoOrder.CreateTime,
		UpdateTime:   daoOrder.UpdateTime,
		// 其他字段从 BizInfo 中解析，这里先设置默认值
		RefundReason: "退款",
	}
}

func convertDomainRefundOrderToDAO(domainOrder domain.RefundOrder) *RefundOrder {
	return &RefundOrder{
		Id:                 uint64(domainOrder.ID),
		OrderId:            uint64(domainOrder.OrderID),
		Status:             int64(domainOrder.Status),
		BuyerRefundAmount:  domainOrder.RefundAmount.Amount(),
		SellerRefundAmount: domainOrder.RefundAmount.Amount(), // 简化处理
		BizInfo:            "", // 可以序列化更多信息到这里
		CreateTime:         domainOrder.CreateTime,
		UpdateTime:         domainOrder.UpdateTime,
	}
}

// 辅助函数
func parseBizInfo(bizInfoStr string) *domain.OrderBizInfo {
	// 这里应该实现 JSON 解析逻辑
	// 暂时返回空对象
	return &domain.OrderBizInfo{}
}

func serializeBizInfo(bizInfo *domain.OrderBizInfo) string {
	// 这里应该实现 JSON 序列化逻辑
	// 暂时返回空字符串
	return ""
}

func extractRoomInfoFromBizInfo(bizInfo *domain.OrderBizInfo) []domain.OrderRoom {
	// 这里应该从 bizInfo 中提取房间信息
	// 暂时返回空切片
	return []domain.OrderRoom{}
}

func extractBookerInfoFromBizInfo(bizInfo *domain.OrderBizInfo) interface{} {
	// 这里应该从 bizInfo 中提取预订人信息
	// 暂时返回 nil
	return nil
}
