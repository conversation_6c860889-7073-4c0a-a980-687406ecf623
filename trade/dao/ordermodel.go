package dao

import (
	"context"
	"database/sql"
	"fmt"
	"hotel/common/types"
	"hotel/trade/domain"
	"strings"

	jsqlx "github.com/jmoiron/sqlx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

type (
	OrderModel struct {
		*defaultOrderModel
	}
)

// NewOrderModel returns a model for the database table.
func NewOrderModel(conn sqlx.SqlConn) *OrderModel {
	return &OrderModel{
		defaultOrderModel: newOrderModel(conn),
	}
}

func (m *OrderModel) withSession(session sqlx.Session) *OrderModel {
	return NewOrderModel(sqlx.NewSqlConnFromSession(session))
}

func (m *OrderModel) FindByIds(ctx context.Context, ids []uint64) ([]*Order, error) {
	return m.defaultOrderModel.FindByIds(ctx, ids)
}

func (m *OrderModel) FindByReferenceNo(ctx context.Context, referenceNo string) (*Order, error) {
	return m.defaultOrderModel.FindByReferenceNo(ctx, referenceNo)
}
func (m *OrderModel) FindByReferenceNos(ctx context.Context, referenceNos []string) ([]*Order, error) {
	baseQuery := fmt.Sprintf("select %s from %s where `reference_no` in (?)", orderRows, m.table)
	query, args, err := jsqlx.In(baseQuery, referenceNos)
	if err != nil {
		return nil, err
	}
	var resp []*Order
	err = m.conn.QueryRowsCtx(ctx, &resp, query, args...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *OrderModel) FindList(ctx context.Context, status ...int64) ([]*Order, error) {
	// 实现按状态查询订单列表，添加分页和限制
	baseQuery := fmt.Sprintf("select %s from %s where `status` in (?) order by create_time desc limit 1000", orderRows, m.defaultOrderModel.table)
	query, args, err := jsqlx.In(baseQuery, status)
	if err != nil {
		return nil, err
	}
	var orders []*Order
	err = m.defaultOrderModel.conn.QueryRowsCtx(ctx, &orders, query, args...)
	return orders, err
}

// FindListWithPagination 分页查询订单列表
func (m *OrderModel) FindListWithPagination(ctx context.Context, state []int64, offset, limit int64) ([]*Order, error) {
	baseQuery := fmt.Sprintf("select %s from %s where `status` in (?) order by create_time desc limit ? offset ?", orderRows, m.defaultOrderModel.table)
	query, args, err := jsqlx.In(baseQuery, state)
	if err != nil {
		return nil, err
	}
	var orders []*Order
	err = m.defaultOrderModel.conn.QueryRowsCtx(ctx, &orders, query, args...)
	return orders, err
}

// CountByStatus 按状态统计订单数量
func (m *OrderModel) CountByStatus(ctx context.Context, state int64) (int64, error) {
	query := fmt.Sprintf("select count(*) from %s where `status` = ?", m.defaultOrderModel.table)
	var count int64
	err := m.defaultOrderModel.conn.QueryRowCtx(ctx, &count, query, state)
	return count, err
}
func (m *OrderModel) Insert(ctx context.Context, data *Order) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, _orderRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Id, data.ReferenceNo, data.UserId, data.Status, data.BuyerCurrency, data.BuyerAmount, data.SellerCurrency, data.SellerAmount, data.TenantRevenueAmountUsd, data.CustomerRevenueAmountUsd, data.RatePkgId, data.CheckIn, data.CheckOut, data.ActualCheckIn, data.ActualCheckOut, data.CustomerEntityId, data.TenantBrandEntityId, data.TenantBusinessEntityId, data.BizInfo)
	return ret, err
}

func (m *OrderModel) Update(ctx context.Context, data *Order) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, _orderRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.ReferenceNo, data.UserId, data.Status, data.BuyerCurrency, data.BuyerAmount, data.SellerCurrency, data.SellerAmount, data.TenantRevenueAmountUsd, data.CustomerRevenueAmountUsd, data.RatePkgId, data.CheckIn, data.CheckOut, data.ActualCheckIn, data.ActualCheckOut, data.CustomerEntityId, data.TenantBrandEntityId, data.TenantBusinessEntityId, data.BizInfo, data.Id)
	return err
}

func (m *OrderModel) UpdateStatus(ctx context.Context, id types.ID, status domain.OrderStatus) error {
	query := fmt.Sprintf("update %s set `status` = ? where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, status.Int64(), id.Int64())
	return err
}

// OrderQueryCriteria 订单查询条件
type OrderQueryCriteria struct {
	PlatformOrderIds   []uint64
	ReferenceNos       []string
	CheckInTimeWindow  *types.TimeWindow
	CheckOutTimeWindow *types.TimeWindow
	CreateTimeWindow   *types.TimeWindow
	CancelTimeWindow   *types.TimeWindow
	StatusList         []int64
	Tags               []string
	Offset             int64
	Limit              int64
}

// FindByCriteria 根据复杂条件查询订单
func (m *OrderModel) FindByCriteria(ctx context.Context, criteria *OrderQueryCriteria) ([]*Order, error) {
	var conditions []string
	var args []any

	// 构建基础查询
	baseQuery := fmt.Sprintf("select %s from %s where 1=1", orderRows, m.table)

	// 添加 ID 条件
	if len(criteria.PlatformOrderIds) > 0 {
		placeholders := strings.TrimRight(strings.Repeat("?,", len(criteria.PlatformOrderIds)), ",")
		conditions = append(conditions, fmt.Sprintf("`id` in (%s)", placeholders))
		for _, id := range criteria.PlatformOrderIds {
			args = append(args, id)
		}
	}

	// 添加外部订单号条件
	if len(criteria.ReferenceNos) > 0 {
		placeholders := strings.TrimRight(strings.Repeat("?,", len(criteria.ReferenceNos)), ",")
		conditions = append(conditions, fmt.Sprintf("`reference_no` in (%s)", placeholders))
		for _, refNo := range criteria.ReferenceNos {
			args = append(args, refNo)
		}
	}

	// 添加状态条件
	if len(criteria.StatusList) > 0 {
		placeholders := strings.TrimRight(strings.Repeat("?,", len(criteria.StatusList)), ",")
		conditions = append(conditions, fmt.Sprintf("`status` in (%s)", placeholders))
		for _, status := range criteria.StatusList {
			args = append(args, status)
		}
	}

	// 添加入住时间窗口条件
	if criteria.CheckInTimeWindow != nil {
		if !criteria.CheckInTimeWindow.Start.IsZero() {
			conditions = append(conditions, "`check_in` >= ?")
			args = append(args, criteria.CheckInTimeWindow.Start)
		}
		if !criteria.CheckInTimeWindow.End.IsZero() {
			conditions = append(conditions, "`check_in` <= ?")
			args = append(args, criteria.CheckInTimeWindow.End)
		}
	}

	// 添加退房时间窗口条件
	if criteria.CheckOutTimeWindow != nil {
		if !criteria.CheckOutTimeWindow.Start.IsZero() {
			conditions = append(conditions, "`check_out` >= ?")
			args = append(args, criteria.CheckOutTimeWindow.Start)
		}
		if !criteria.CheckOutTimeWindow.End.IsZero() {
			conditions = append(conditions, "`check_out` <= ?")
			args = append(args, criteria.CheckOutTimeWindow.End)
		}
	}

	// 添加创建时间窗口条件
	if criteria.CreateTimeWindow != nil {
		if !criteria.CreateTimeWindow.Start.IsZero() {
			conditions = append(conditions, "`create_time` >= ?")
			args = append(args, criteria.CreateTimeWindow.Start)
		}
		if !criteria.CreateTimeWindow.End.IsZero() {
			conditions = append(conditions, "`create_time` <= ?")
			args = append(args, criteria.CreateTimeWindow.End)
		}
	}

	// 添加取消时间窗口条件 (假设有 cancel_time 字段)
	if criteria.CancelTimeWindow != nil {
		if !criteria.CancelTimeWindow.Start.IsZero() {
			conditions = append(conditions, "`update_time` >= ? AND `status` = ?")
			args = append(args, criteria.CancelTimeWindow.Start, 6) // 6 是取消状态
		}
		if !criteria.CancelTimeWindow.End.IsZero() {
			conditions = append(conditions, "`update_time` <= ? AND `status` = ?")
			args = append(args, criteria.CancelTimeWindow.End, 6)
		}
	}

	// 构建完整查询
	if len(conditions) > 0 {
		baseQuery += " AND " + strings.Join(conditions, " AND ")
	}

	// 添加排序
	baseQuery += " ORDER BY create_time DESC"

	// 添加分页
	if criteria.Limit > 0 {
		baseQuery += " LIMIT ?"
		args = append(args, criteria.Limit)
		if criteria.Offset > 0 {
			baseQuery += " OFFSET ?"
			args = append(args, criteria.Offset)
		}
	}

	var resp []*Order
	err := m.conn.QueryRowsCtx(ctx, &resp, baseQuery, args...)
	return resp, err
}

// CountByCriteria 根据条件统计订单数量
func (m *OrderModel) CountByCriteria(ctx context.Context, criteria *OrderQueryCriteria) (int64, error) {
	var conditions []string
	var args []any

	baseQuery := fmt.Sprintf("select count(*) from %s where 1=1", m.table)

	// 复用相同的条件构建逻辑（不包括分页）
	if len(criteria.PlatformOrderIds) > 0 {
		placeholders := strings.TrimRight(strings.Repeat("?,", len(criteria.PlatformOrderIds)), ",")
		conditions = append(conditions, fmt.Sprintf("`id` in (%s)", placeholders))
		for _, id := range criteria.PlatformOrderIds {
			args = append(args, id)
		}
	}

	if len(criteria.ReferenceNos) > 0 {
		placeholders := strings.TrimRight(strings.Repeat("?,", len(criteria.ReferenceNos)), ",")
		conditions = append(conditions, fmt.Sprintf("`reference_no` in (%s)", placeholders))
		for _, refNo := range criteria.ReferenceNos {
			args = append(args, refNo)
		}
	}

	if len(criteria.StatusList) > 0 {
		placeholders := strings.TrimRight(strings.Repeat("?,", len(criteria.StatusList)), ",")
		conditions = append(conditions, fmt.Sprintf("`status` in (%s)", placeholders))
		for _, status := range criteria.StatusList {
			args = append(args, status)
		}
	}

	// 添加时间窗口条件（与上面相同的逻辑）
	if criteria.CheckInTimeWindow != nil {
		if !criteria.CheckInTimeWindow.Start.IsZero() {
			conditions = append(conditions, "`check_in` >= ?")
			args = append(args, criteria.CheckInTimeWindow.Start)
		}
		if !criteria.CheckInTimeWindow.End.IsZero() {
			conditions = append(conditions, "`check_in` <= ?")
			args = append(args, criteria.CheckInTimeWindow.End)
		}
	}

	if criteria.CheckOutTimeWindow != nil {
		if !criteria.CheckOutTimeWindow.Start.IsZero() {
			conditions = append(conditions, "`check_out` >= ?")
			args = append(args, criteria.CheckOutTimeWindow.Start)
		}
		if !criteria.CheckOutTimeWindow.End.IsZero() {
			conditions = append(conditions, "`check_out` <= ?")
			args = append(args, criteria.CheckOutTimeWindow.End)
		}
	}

	if criteria.CreateTimeWindow != nil {
		if !criteria.CreateTimeWindow.Start.IsZero() {
			conditions = append(conditions, "`create_time` >= ?")
			args = append(args, criteria.CreateTimeWindow.Start)
		}
		if !criteria.CreateTimeWindow.End.IsZero() {
			conditions = append(conditions, "`create_time` <= ?")
			args = append(args, criteria.CreateTimeWindow.End)
		}
	}

	if criteria.CancelTimeWindow != nil {
		if !criteria.CancelTimeWindow.Start.IsZero() {
			conditions = append(conditions, "`update_time` >= ? AND `status` = ?")
			args = append(args, criteria.CancelTimeWindow.Start, 6)
		}
		if !criteria.CancelTimeWindow.End.IsZero() {
			conditions = append(conditions, "`update_time` <= ? AND `status` = ?")
			args = append(args, criteria.CancelTimeWindow.End, 6)
		}
	}

	if len(conditions) > 0 {
		baseQuery += " AND " + strings.Join(conditions, " AND ")
	}

	var count int64
	err := m.conn.QueryRowCtx(ctx, &count, baseQuery, args...)
	return count, err
}

var (
	_orderRowsExpectAutoSet   = strings.Join(stringx.Remove(orderFieldNames, "`create_time`", "`update_time`"), ",")
	_orderRowsWithPlaceHolder = strings.Join(stringx.Remove(orderFieldNames, "`id`", "`create_time`", "`update_time`"), "=?,") + "=?"
)
