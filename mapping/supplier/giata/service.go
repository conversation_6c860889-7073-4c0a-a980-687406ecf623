package giata

import (
	"bytes"
	"context"
	"encoding/xml"
	"errors"
	"hotel/mapping/supplier/giata/model"
	rmDomain "hotel/mapping/supplier/olivier/domain"
	"hotel/supplier/domain"
	"hotel/supplier/middleware"

	"golang.org/x/net/html/charset"
)

type GiataClient struct {
	*middleware.SupplierUtilWrapper[model.Properties]
}

func (s *GiataClient) Supplier() domain.Supplier {
	return domain.Supplier_Giata
}

// 获取单个酒店静态详情
func (s *GiataClient) HotelStaticDetail(ctx context.Context, req *domain.HotelStaticDetailReq) (*domain.HotelStaticDetailResp, error) {
	// GIATA API需要特定的查询参数格式
	giataReq := map[string]string{
		"sc":   "hotel",
		"vc":   "GIATA", // 供应商代码，需要根据实际情况调整
		"oc":   req.SupplierHotelId,
		"show": "fact,hn,vn",
	}
	// 用string接收原始响应
	var respXml string
	if err := s.Execute(ctx, domain.APIName_HotelStaticDetail, giataReq, &respXml); err != nil {
		return nil, err
	}
	// 用encoding/xml+charset.NewReaderLabel解码
	decoder := xml.NewDecoder(bytes.NewReader([]byte(respXml)))
	decoder.CharsetReader = charset.NewReaderLabel
	giataResp := new(model.HotelDetailsResponse)
	if err := decoder.Decode(giataResp); err != nil {
		return nil, err
	}
	convertedResult := ConvertHotelDetailsResponse2HotelListResp(giataResp)
	if len(convertedResult.Hotels) == 0 {
		return nil, nil
	}
	return &domain.HotelStaticDetailResp{
		HotelStaticProfile: convertedResult.Hotels[0].HotelStaticProfile,
	}, nil
}

// 批量获取酒店静态详情
func (s *GiataClient) batchHotelStaticDetail(ctx context.Context, supplierHotelIds []string) (*domain.BatchHotelStaticDetailResp, error) {
	// GIATA API需要逐个查询，暂不支持批量
	var hotels []domain.SupplierHotel
	for _, hotelId := range supplierHotelIds {
		detail, err := s.HotelStaticDetail(ctx, &domain.HotelStaticDetailReq{
			SupplierHotelId: hotelId,
		})
		if err != nil {
			continue // 跳过错误的酒店ID
		}
		if detail != nil {
			hotels = append(hotels, domain.SupplierHotel{
				SupplierHotelId:    hotelId,
				Supplier:           domain.Supplier_Giata,
				HotelStaticProfile: detail.HotelStaticProfile,
			})
		}
	}
	return &domain.BatchHotelStaticDetailResp{
		Hotels: hotels,
	}, nil
}

// 获取酒店列表
func (s *GiataClient) HotelList(ctx context.Context, req *domain.HotelListReq) (*domain.HotelListResp, error) {
	var supplierHotelIds []string
	if len(req.SupplierHotelIds) == 0 {
		// TODO: GIATA酒店ID列表获取逻辑，需根据实际API实现
		return nil, nil
	} else {
		supplierHotelIds = req.SupplierHotelIds
	}
	hotelDetails, err := s.batchHotelStaticDetail(ctx, supplierHotelIds)
	if err != nil {
		return nil, err
	}
	return &domain.HotelListResp{
		Hotels: hotelDetails.Hotels,
	}, nil
}

// HotelIdList 获取酒店ID列表（GIATA暂不支持，需补充API文档后实现）
func (s *GiataClient) HotelIdList(ctx context.Context, req *domain.HotelIdListReq) (*domain.HotelIdListResp, error) {
	return nil, errors.New("HotelIdList not implemented for GIATA")
}

// HotelRates 获取酒店房型和价格（GIATA暂不支持，需补充API文档后实现）
func (s *GiataClient) HotelRates(ctx context.Context, req *domain.HotelRatesReq) (*domain.HotelRatesResp, error) {
	return nil, errors.New("HotelRates not implemented for GIATA")
}

// CheckAvail 查询是否可以下定（GIATA暂不支持，需补充API文档后实现）
func (s *GiataClient) CheckAvail(ctx context.Context, req *domain.CheckAvailReq) (*domain.CheckAvailResp, error) {
	return nil, errors.New("CheckAvail not implemented for GIATA")
}

// Book 下单（GIATA暂不支持，需补充API文档后实现）
func (s *GiataClient) Book(ctx context.Context, req *domain.BookReq) (*domain.BookResp, error) {
	return nil, errors.New("Book not implemented for GIATA")
}

// QueryOrderByIDs 获取订单列表（GIATA暂不支持，需补充API文档后实现）
func (s *GiataClient) QueryOrderByIDs(ctx context.Context, req *domain.QueryOrdersReq) (*domain.QueryOrdersResp, error) {
	return nil, errors.New("QueryOrderByIDs not implemented for GIATA")
}

// Cancel 取消订单（GIATA暂不支持，需补充API文档后实现）
func (s *GiataClient) Cancel(ctx context.Context, req *domain.CancelReq) (*domain.CancelResp, error) {
	return nil, errors.New("Cancel not implemented for GIATA")
}

// RoomMatching provides GIATA-based room matching using multicodes
func (s *GiataClient) RoomMatching(ctx context.Context, req *rmDomain.RoomMatchingReq) (*rmDomain.RoomMatchingResp, error) {
	// GIATA room mapping implementation
	// For now, return a basic response - full implementation would integrate with GIATA Multicodes API

	resp := &rmDomain.RoomMatchingResp{
		Matching: make([]rmDomain.RoomMatch, 0),
	}

	// Group rooms by similar names for basic matching
	roomGroups := s.groupRoomsByName(req.List)

	for roomName, rooms := range roomGroups {
		if len(rooms) > 1 { // Only create matches for rooms with multiple suppliers
			match := rmDomain.RoomMatch{
				RoomName:  roomName,
				Suppliers: make([]rmDomain.RoomMatchSupplierItem, 0),
			}

			for _, room := range rooms {
				supplierItem := rmDomain.RoomMatchSupplierItem{
					Supplier: room.Supplier,
					HotelID:  room.HotelID,
					RoomKey: rmDomain.RoomKey{
						RoomID:     room.RoomCode1, // Use RoomCode1 as RoomID
						RoomCode:   room.RoomCode2, // Use RoomCode2 as RoomCode
						Confidence: 0.8,            // Basic confidence score
					},
				}
				match.Suppliers = append(match.Suppliers, supplierItem)
			}

			resp.Matching = append(resp.Matching, match)
		}
	}

	return resp, nil
}

// groupRoomsByName groups rooms by similar names for basic matching
func (s *GiataClient) groupRoomsByName(rooms []rmDomain.RoomInfo) map[string][]rmDomain.RoomInfo {
	groups := make(map[string][]rmDomain.RoomInfo)

	for _, room := range rooms {
		// Simple grouping by room name
		// In a full implementation, this would use GIATA's standardized room types
		roomName := room.Name
		if roomName == "" {
			roomName = "Unknown"
		}

		groups[roomName] = append(groups[roomName], room)
	}

	return groups
}

// For future GIATA Multicodes integration
func (s *GiataClient) getGiataRoomTypes(ctx context.Context, hotelID string) (*model.GiataRoomTypesResponse, error) {
	// Future implementation for GIATA Multicodes API
	giataReq := map[string]string{
		"sc":   "roomtype",
		"vc":   "GIATA",
		"oc":   hotelID,
		"show": "fact,rt",
	}

	var respXml string
	if err := s.Execute(ctx, domain.APIName_HotelStaticDetail, giataReq, &respXml); err != nil {
		return nil, err
	}

	giataResp := new(model.GiataRoomTypesResponse)
	if err := xml.Unmarshal([]byte(respXml), giataResp); err != nil {
		return nil, err
	}

	return giataResp, nil
}
