# GIATA Supplier Implementation

## Overview

GIATA供应商集成，基于GIATA XML Web Service和FACTS API实现酒店静态信息获取功能。

## Features

- ✅ 配置文件加载和验证
- ✅ HTTP Basic Authentication认证
- ✅ 酒店静态详情查询 (HotelStaticDetail)
- ✅ 批量酒店信息查询 (HotelList)
- ✅ 房间匹配服务 (RoomMatching)
- ✅ 错误处理和日志记录
- ✅ 单元测试覆盖

## Configuration

配置文件: `config.yaml`

```yaml
redis:
  host: "127.0.0.1:6379"
  type: "node"
  user: ""
  pass: ""
  tls: false
  nonBlock: true
  pingTimeout: "1s"

API:
  HotelStaticDetail:
    baseURL: "https://xml.giatamedia.com"
    path: "/" # GIATA XML Web Service端点
    httpMethod: "GET"
    timeoutSeconds: 30
    enableEncode: false

# GIATA 测试账号
client:
  ClientID: "giata|ttdbooking.com"
  LicenseKey: "@ah5pKniLUtRRtk"
```

## API Documentation

### GIATA XML Web Service

- **Base URL**: `https://xml.giatamedia.com`
- **Authentication**: HTTP Basic Auth
- **Documentation**: <https://xml.giatamedia.com/dokumentation/GIATA-Facts/GIATA-Facts-en.html>

### Request Parameters

- `sc`: Service code (e.g., "hotel")
- `vc`: Vendor/supplier code
- `oc`: Object code (hotel ID)
- `show`: Data to show (e.g., "fact,hn,vn")

## Code Structure

```
supplier/giata/
├── config.yaml           # 配置文件
├── init.go               # 初始化和依赖注入
├── service.go            # 核心服务实现
├── service_util.go       # 服务工具方法
├── converter.go          # 数据转换器
├── config_test.go        # 配置测试
├── service_test.go       # 服务测试
├── model/
│   ├── base.go          # 基础模型和认证
│   └── static.go        # 静态数据模型
└── README.md            # 文档
```

## Usage

### Initialization

```go
client := giata.NewGiataClient()
```

### Hotel Static Detail

```go
ctx := context.Background()
req := &domain.HotelStaticDetailReq{
    SupplierHotelId: "hotel123",
}
resp, err := client.HotelStaticDetail(ctx, req)
```

### Hotel List

```go
ctx := context.Background()
req := &domain.HotelListReq{
    SupplierHotelIds: []string{"hotel1", "hotel2"},
}
resp, err := client.HotelList(ctx, req)
```

### Room Matching

```go
ctx := context.Background()
req := &rmDomain.RoomMatchingReq{
    Iso2: "US",
    List: []rmDomain.RoomInfo{
        {
            HotelID:   "hotel123",
            Supplier:  "dida",
            RoomCode1: "room001",
            RoomCode2: "rate001",
            Name:      "Deluxe Room",
        },
    },
}
resp, err := client.RoomMatching(ctx, req)
```

## Testing

运行所有测试:

```bash
cd supplier/giata
go test -v
```

运行特定测试:

```bash
go test -v -run TestConfigLoad
go test -v -run TestGiataService_HotelList
go test -v -run TestGiataService_HotelStaticDetail
```

## Authentication

GIATA API使用HTTP Basic Authentication:

- Username: ClientID (e.g., "giata|ttdbooking.com")
- Password: LicenseKey (e.g., "@ah5pKniLUtRRtk")

认证通过`Header`结构的`GetUsernameAndPassword()`方法实现。

## Error Handling

- 401 Unauthorized: 认证失败，检查ClientID和LicenseKey
- 404 Not Found: API端点错误或酒店ID不存在
- 网络错误: 自动重试3次

## Known Issues

1. **认证问题**: 当前测试账号可能权限不足，返回401错误
2. **批量查询**: GIATA API不支持真正的批量查询，需要逐个调用
3. **供应商代码**: 需要根据实际情况调整vendor code (vc参数)

## Next Steps

1. 获取有效的GIATA API凭证
2. 实现更多API端点 (如房型信息、价格查询等)
3. 添加缓存机制提高性能
4. 完善错误处理和重试逻辑
5. 添加更多单元测试和集成测试

## References

- [GIATA Official Website](https://www.giata.com/)
- [GIATA MultiCodes](https://www.giata.com/en/products-services/hotel-mapping-for-otas/)
- [GIATA FACTS API Documentation](https://xml.giatamedia.com/dokumentation/GIATA-Facts/GIATA-Facts-en.html)
- [GIATA Multilingual Hotel Guide](https://www.giata.com/en/products-services/multilingual-hotel-guide/)
